[workspace]
members = [
    "crates/types",
    "crates/blob",
    "crates/parity-base",
    "crates/parity-lib",
    "crates/parity-root",
    "crates/private-kernel-lib",
    "crates/private-kernel-init",
    "crates/private-kernel-init-simulated",
    "crates/private-kernel-inner",
    "crates/private-kernel-inner-simulated",
    "crates/private-kernel-reset",
    "crates/private-kernel-reset-simulated",
    "crates/private-kernel-tail",
    "crates/private-kernel-tail-simulated",
    "crates/private-kernel-tail-to-public",
    "crates/private-kernel-tail-to-public-simulated",
    "crates/reset-kernel-lib",
    "crates/rollup-lib",
    "crates/rollup-merge",
    "crates/rollup-base-private",
    "crates/rollup-base-private-simulated",
    "crates/rollup-base-public",
    "crates/rollup-base-public-simulated",
    "crates/rollup-block-merge",
    "crates/rollup-block-root",
    "crates/rollup-block-root-simulated",
    "crates/rollup-block-root-single-tx",
    "crates/rollup-block-root-single-tx-simulated",
    "crates/rollup-block-root-empty",
    "crates/rollup-block-root-padding",
    "crates/rollup-root",
]
