[previous_kernel.vk_data]
leaf_index = "0x0000000000000000000000000000000000000000000000000000000000000000"
sibling_path = [
  "0x129f85ef83fbdd0dc2a0c4683113bb1b997f1e5fad7ec537877881fab296ed39",
  "0x18391b634c41483b1c12a1ab9cf6f7c7a1178cbf04be39a8028987845c2c02b4",
  "0x25658408efe238363e8dea27ae709c963fcef450df5c99142169b106192a07f1",
  "0x1ce542346cdf2ff978d6be2a0e58df869bebc9d1885a7f31774e92ab43a706f1",
  "0x151834d07c5c1d4ca68e0799896475ef10f1185905075f19f14b37a9faf3aadc",
  "0x16cf26363cc6eaf45b281d8edc651c7fa55763efa49935e654444a77dbf001a5"
]

  [previous_kernel.vk_data.vk]
  key = [
  "0x0000000000000000000000000000000000000000000000000000000000040000",
  "0x0000000000000000000000000000000000000000000000000000000000000020",
  "0x0000000000000000000000000000000000000000000000000000000000005609",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000018",
  "0x0000000000000000000000000000000000000000000000000000000000000010",
  "0x0000000000000000000000000000000000000000000000000000000000000001",
  "0x000000000000000000000000000000e3ea4a807055241c662d5ad250d2233909",
  "0x00000000000000000000000000000000002cc7e2c6963df87edff117371fa44a",
  "0x00000000000000000000000000000067229f6f3bdcc1d6d51582b95a6e2f468a",
  "0x00000000000000000000000000000000001d68604d6f4ad11dfab3ced14d0e8a",
  "0x0000000000000000000000000000002d92d2ff6245a02d9592ddc7656e5c1c95",
  "0x0000000000000000000000000000000000172200c802f50edad9853fa68773c0",
  "0x00000000000000000000000000000088ba34881505fb9c43b24950a4adb60d4a",
  "0x00000000000000000000000000000000000f4898718c0526750f318766cb06b9",
  "0x00000000000000000000000000000045e8740dddfaf537caa90c92fef5c5cce8",
  "0x00000000000000000000000000000000001285c8569a612e3264847863c337da",
  "0x000000000000000000000000000000a448de8476d01299f4feb1c0a68256c267",
  "0x00000000000000000000000000000000000ded12ec0a4c0936951c125d326225",
  "0x000000000000000000000000000000b12eaa423bba0509a29846722f46412e40",
  "0x0000000000000000000000000000000000226cf469c27441627816e4b48aaee7",
  "0x00000000000000000000000000000065325ea7ca2a268ad649f749a8d819ae6e",
  "0x00000000000000000000000000000000001e4d9a3243f5bd7f7e757c52816968",
  "0x000000000000000000000000000000828e1760f680a507ac229d0a067e7c66ea",
  "0x00000000000000000000000000000000002148ac7d8093966d9f9ba0e0f542a5",
  "0x000000000000000000000000000000c2fc7059f08b8d248dfd66fe6ca29ece37",
  "0x0000000000000000000000000000000000099507e32a50144c41414177e6bc2a",
  "0x00000000000000000000000000000066185a74b171b820904ba2b55f52ffc885",
  "0x00000000000000000000000000000000001b80096b34949a0e8d66562ea56b86",
  "0x0000000000000000000000000000000868c3053204b53c8dfe48b378ea929add",
  "0x0000000000000000000000000000000000189fc7987a97f237412a49fe678f77",
  "0x000000000000000000000000000000e86029d204645db79b8351b8614978b637",
  "0x00000000000000000000000000000000001fef67f0dbbb8d1c98d894cb3362f9",
  "0x000000000000000000000000000000b638a4034d6f4d1d4af78ab67813170524",
  "0x00000000000000000000000000000000000a7c9d8568b8b4af133c35f2e528a8",
  "0x00000000000000000000000000000090d53c6a3b26339cda6b73df9208314159",
  "0x0000000000000000000000000000000000298c3311fc9170f92de940b042aab9",
  "0x000000000000000000000000000000bf37537eb196b05c1d98fa51016a9bacbb",
  "0x000000000000000000000000000000000007b05f408a612847259016b9204ae4",
  "0x0000000000000000000000000000005f2d3d1735e87996d3daf2c340ab5665a3",
  "0x00000000000000000000000000000000000307e68252e74cb72d84c17a6d1329",
  "0x000000000000000000000000000000fab608077a2f289eaed40e98915ac657cc",
  "0x000000000000000000000000000000000001acaf271ae4d1677d4845505afad5",
  "0x0000000000000000000000000000008dc06bbe7761b9e9d7a724a478d1c7ca7b",
  "0x000000000000000000000000000000000021909a8479687c331b07f6e1845940",
  "0x0000000000000000000000000000000b2a3fd8ad30a32947a41b493f092d8053",
  "0x000000000000000000000000000000000016bfb102ddbbdc52a1c2f4aa83bcad",
  "0x000000000000000000000000000000ce32a645a1cf8b5f086358ffd8c47d8c05",
  "0x000000000000000000000000000000000019b3ee1337ece60c6446831ce03496",
  "0x000000000000000000000000000000bb69842382adb7b11b6869af31207027b0",
  "0x00000000000000000000000000000000002679f084f27ba125560ba8f0ab3a08",
  "0x000000000000000000000000000000d9faf91579ad171db0f51eb68fb66a3f6a",
  "0x00000000000000000000000000000000000d1d2525a4505f5c9bc762caf9ce25",
  "0x000000000000000000000000000000129ca422e4fdc316864a0434437dc1eb7e",
  "0x00000000000000000000000000000000002faac3061a5f0bcdf6d0d9f27fcd9f",
  "0x0000000000000000000000000000003aadd07fd757de86d5560ad7aeaa08c69e",
  "0x000000000000000000000000000000000018b0313d9d92d076b588a27a98041c",
  "0x0000000000000000000000000000002bc94efb71a86353847df22969346058a7",
  "0x000000000000000000000000000000000028fb2ca142004850f213a8c7b556c2",
  "0x000000000000000000000000000000d6b2a08b5782940fb14863ee328eb02470",
  "0x00000000000000000000000000000000002429a4ec26b8a0d657cfe9c821e2bc",
  "0x000000000000000000000000000000752991b39bc348a4ca279a2892d83c1e7c",
  "0x0000000000000000000000000000000000068eb44d1955c97baef4284e2e565d",
  "0x000000000000000000000000000000c1eba2ce697b5e5c8c61508918cf9c987c",
  "0x000000000000000000000000000000000012e48fd875084773e76c4bacfcec13",
  "0x000000000000000000000000000000136a961ef92874f4b9c01dc3f269083ccf",
  "0x0000000000000000000000000000000000221dfd95f325cf937d7d1616af6572",
  "0x00000000000000000000000000000061d84d4d3567b50974bf5c85fbad70ac4b",
  "0x00000000000000000000000000000000000f7a49ba9e4d4ecb4a762b0d577812",
  "0x000000000000000000000000000000d5adc98bf3aa4eabb280aa70520ef43a1c",
  "0x00000000000000000000000000000000000cea208abb068c5160165469bc6083",
  "0x000000000000000000000000000000f05f553d4a34fbc470653aaca6c9afbd97",
  "0x000000000000000000000000000000000015ad148255388c84de0d16b2ed9c57",
  "0x0000000000000000000000000000006226b4429cb7a88acaf3e11c66a020a396",
  "0x00000000000000000000000000000000000dac87073b20c9cf8b2bc004e6ff10",
  "0x000000000000000000000000000000f3c1e7b88dd8a29c2aed3cb4f72b279999",
  "0x00000000000000000000000000000000002471f08f3c0779b0ac6ba7ad785168",
  "0x00000000000000000000000000000020bfd8e58fdffd89985d1693bbb6ce726c",
  "0x0000000000000000000000000000000000035d54b07b087ce1e002c616e2e8b6",
  "0x000000000000000000000000000000b2c5ac73a9f5710b900164a9bd0e4fb28a",
  "0x00000000000000000000000000000000000443af4fc3708c02347161829ad89d",
  "0x000000000000000000000000000000baff031c060209aea0157a6e60bdb7e21f",
  "0x000000000000000000000000000000000015c33c84a36cee1ba92527db56bf97",
  "0x000000000000000000000000000000dc9a3ae0129864d7304e0e8482a084a248",
  "0x0000000000000000000000000000000000021cc2185a58a6d60a9a9c6b139bd3",
  "0x000000000000000000000000000000eb44484ae6d462486add5844aa98510cd0",
  "0x00000000000000000000000000000000000df728daa60f5e9cc33d371e50fab8",
  "0x000000000000000000000000000000677a2c701a71f8c8afb044699ac76337d7",
  "0x000000000000000000000000000000000007c4649235f8f5a6908550d7e0784d",
  "0x000000000000000000000000000000cb148dc361e473730f413d1511ceef68de",
  "0x0000000000000000000000000000000000054259b5d5b260854c05410cef131e",
  "0x0000000000000000000000000000000cfaaaa9b131295141b15f478980281e5c",
  "0x00000000000000000000000000000000002766fbe395b9d9850cf4eff41a32b3",
  "0x00000000000000000000000000000091be350e7c1ca4fa3bb7fda0ca515bcbba",
  "0x000000000000000000000000000000000030081467081c71a7cc0fb702feff86",
  "0x000000000000000000000000000000f6f4596202301b6ae4eb0ebbeadd203340",
  "0x00000000000000000000000000000000000adc89c48d75b571636f5bbeb4a806",
  "0x00000000000000000000000000000000034e3e27454ef992b4bf84b97baa7471",
  "0x0000000000000000000000000000000000066f28135748f119631c3fe07fa9d7",
  "0x0000000000000000000000000000003b64a66f2ac4979b65e56568c5a31b14ed",
  "0x00000000000000000000000000000000002e25783551df50c004ec7cd1f4dd8b",
  "0x000000000000000000000000000000e8258f84477c1b62565a559ba7bb38832e",
  "0x000000000000000000000000000000000018f76cf0ceeccb4798de741ae89b64",
  "0x000000000000000000000000000000353d43fa70e99239c1c1c67e271a0eeac5",
  "0x00000000000000000000000000000000002d299fb68678d0150bcc5b16dc8252",
  "0x0000000000000000000000000000002814ede7cd27daed00c33c12860bc4b046",
  "0x000000000000000000000000000000000015d3ac5a199abb74933a4efc98c59b",
  "0x000000000000000000000000000000469680c270e551515344592f59188fa765",
  "0x00000000000000000000000000000000002d38d6d4ba1e4763a74ecdb11ca1f3",
  "0x000000000000000000000000000000fce917c0d5dca019477c52f6075332b612",
  "0x000000000000000000000000000000000012db39e892826b32610ee08251e005",
  "0x0000000000000000000000000000000000000000000000000000000000000001",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000002",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x000000000000000000000000000000ea76811855c0bb1462d42be62ae27f7c7b",
  "0x0000000000000000000000000000000000024ce07fd779d1038323cdf136537c",
  "0x00000000000000000000000000000042f740bf9767e4dc722ba5c3be9c1267dd",
  "0x0000000000000000000000000000000000079a40035a650f98eebd632fb58744",
  "0x000000000000000000000000000000f56eaf77f94da8eecc839a504cf00b3093",
  "0x0000000000000000000000000000000000303d997a5508aad972bd110770490f",
  "0x000000000000000000000000000000f3dc775c979bcc733c6c0de18146b7b0cb",
  "0x00000000000000000000000000000000002f57d2fa925b4fc915d77dd2095127",
  "0x000000000000000000000000000000e55ba19751adfe6c36324d3fb6c2da0989",
  "0x00000000000000000000000000000000001d58aa61c64ad522043d79c4802219",
  "0x00000000000000000000000000000078f4b3bc61f19d6e7069359bbf47e7f907",
  "0x00000000000000000000000000000000002d7c18a93c3dae58809faaeec6a86a"
]
  hash = "0x274f7ab735ca280bc2a9412aa05b106c3664921b13d6243e705130035d5a8f2a"

[previous_kernel_public_inputs]
min_revertible_side_effect_counter = "0x0000000000000000000000000000000000000000000000000000000000000003"
is_private_only = true
claimed_first_nullifier = "0x27e956c18dc1225871b3e6e277ea0c9d30058e095e4818da55052f7c994bfe8e"

  [previous_kernel_public_inputs.constants]
  vk_tree_root = "0x1ee6a40f9c295dbdbcaf4eafcba42c488330caff248419f6a0100ed9d5b2719c"
  protocol_contract_tree_root = "0x0dbfba3eddc8d0560547d65dac94beb59c45bb56de9df596e338505b620fa335"

    [previous_kernel_public_inputs.constants.historical_header]
    total_fees = "0x00000000000000000000000000000000000000000000000000000000741958c4"
    total_mana_used = "0x000000000000000000000000000000000000000000000000000000000000d23e"

      [previous_kernel_public_inputs.constants.historical_header.last_archive]
      root = "0x07d03443b8719aac8b4b54ab132940fc21800149845bd28697ed3baeaa21a523"
      next_available_leaf_index = "0x0000000000000000000000000000000000000000000000000000000000000006"

      [previous_kernel_public_inputs.constants.historical_header.content_commitment]
      blobs_hash = "0x000c0a16f870bff67605e8c18de39068658cbafa5444fff90752364563815ec6"
      in_hash = "0x00089a9d421a82c4a25f7acbebe69e638d5b064fa8a60e018793dcb0be53752c"
      out_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.constants.historical_header.state.l1_to_l2_message_tree]
root = "0x2e33ee2008411c04b99c24b313513d097a0d21a5040b6193d1f978b8226892d6"
next_available_leaf_index = "0x0000000000000000000000000000000000000000000000000000000000000060"

[previous_kernel_public_inputs.constants.historical_header.state.partial.note_hash_tree]
root = "0x10c1f741a0fa706e1eddb3bf23cb6f1305bfb60d621af66f5427d0d67d0b312a"
next_available_leaf_index = "0x0000000000000000000000000000000000000000000000000000000000000180"

[previous_kernel_public_inputs.constants.historical_header.state.partial.nullifier_tree]
root = "0x0e4db848d4729ffb24a540d50688a3db38e38460da4eadd9a13308b2405ec6d9"
next_available_leaf_index = "0x0000000000000000000000000000000000000000000000000000000000000200"

[previous_kernel_public_inputs.constants.historical_header.state.partial.public_data_tree]
root = "0x1fcc6dea40abf7522d330bc6c1d187fc476f4a3c3af9f16d9dc2c4c0cb8ff092"
next_available_leaf_index = "0x000000000000000000000000000000000000000000000000000000000000008a"

      [previous_kernel_public_inputs.constants.historical_header.global_variables]
      chain_id = "0x0000000000000000000000000000000000000000000000000000000000007a69"
      version = "0x0000000000000000000000000000000000000000000000000000000055c58b0d"
      block_number = "0x0000000000000000000000000000000000000000000000000000000000000006"
      slot_number = "0x0000000000000000000000000000000000000000000000000000000000000007"
      timestamp = "0x00000000000000000000000000000000000000000000000000000000684882f2"

        [previous_kernel_public_inputs.constants.historical_header.global_variables.coinbase]
        inner = "0x0000000000000000000000008e7508851cd7c32bc45138c6315abb45e66adbbb"

        [previous_kernel_public_inputs.constants.historical_header.global_variables.fee_recipient]
        inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

        [previous_kernel_public_inputs.constants.historical_header.global_variables.gas_fees]
        fee_per_da_gas = "0x0000000000000000000000000000000000000000000000000000000000000000"
        fee_per_l2_gas = "0x0000000000000000000000000000000000000000000000000000000000008d5e"

    [previous_kernel_public_inputs.constants.tx_context]
    chain_id = "0x0000000000000000000000000000000000000000000000000000000000007a69"
    version = "0x0000000000000000000000000000000000000000000000000000000055c58b0d"

[previous_kernel_public_inputs.constants.tx_context.gas_settings.gas_limits]
da_gas = "0x000000000000000000000000000000000000000000000000000000003b9aca00"
l2_gas = "0x000000000000000000000000000000000000000000000000000000003b9aca00"

[previous_kernel_public_inputs.constants.tx_context.gas_settings.teardown_gas_limits]
da_gas = "0x00000000000000000000000000000000000000000000000000000000005b8d80"
l2_gas = "0x00000000000000000000000000000000000000000000000000000000005b8d80"

[previous_kernel_public_inputs.constants.tx_context.gas_settings.max_fees_per_gas]
fee_per_da_gas = "0x0000000000000000000000000000000000000000000000000000000000000000"
fee_per_l2_gas = "0x000000000000000000000000000000000000000000000000000000000000d40d"

[previous_kernel_public_inputs.constants.tx_context.gas_settings.max_priority_fees_per_gas]
fee_per_da_gas = "0x0000000000000000000000000000000000000000000000000000000000000000"
fee_per_l2_gas = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.for_rollup.max_block_number._opt]
_is_some = true
_value = "0x0000000000000000000000000000000000000000000000000000000000000e16"

[[previous_kernel_public_inputs.validation_requests.note_hash_read_requests]]
[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.read_request]
value = "0x2a40d7e8d09b7119d5950f4bb67494f83511bbefb12c017077dc480abdd229ac"
counter = "0x0000000000000000000000000000000000000000000000000000000000000002"

[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.contract_address]
inner = "0x2bd6622f715bd307d4918e462bd9aa829794c19bfe8ebbe5390237361e65e437"

[[previous_kernel_public_inputs.validation_requests.note_hash_read_requests]]
[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.note_hash_read_requests]]
[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.note_hash_read_requests]]
[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.note_hash_read_requests]]
[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.note_hash_read_requests]]
[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.note_hash_read_requests]]
[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.note_hash_read_requests]]
[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.note_hash_read_requests]]
[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.note_hash_read_requests]]
[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.note_hash_read_requests]]
[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.note_hash_read_requests]]
[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.note_hash_read_requests]]
[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.note_hash_read_requests]]
[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.note_hash_read_requests]]
[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.note_hash_read_requests]]
[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.note_hash_read_requests]]
[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.note_hash_read_requests]]
[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.note_hash_read_requests]]
[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.note_hash_read_requests]]
[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.note_hash_read_requests]]
[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.note_hash_read_requests]]
[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.note_hash_read_requests]]
[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.note_hash_read_requests]]
[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.note_hash_read_requests]]
[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.note_hash_read_requests]]
[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.note_hash_read_requests]]
[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.note_hash_read_requests]]
[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.note_hash_read_requests]]
[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.note_hash_read_requests]]
[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.note_hash_read_requests]]
[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.note_hash_read_requests]]
[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.note_hash_read_requests]]
[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.note_hash_read_requests]]
[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.note_hash_read_requests]]
[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.note_hash_read_requests]]
[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.note_hash_read_requests]]
[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.note_hash_read_requests]]
[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.note_hash_read_requests]]
[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.note_hash_read_requests]]
[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.note_hash_read_requests]]
[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.note_hash_read_requests]]
[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.note_hash_read_requests]]
[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.note_hash_read_requests]]
[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.note_hash_read_requests]]
[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.note_hash_read_requests]]
[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.note_hash_read_requests]]
[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.note_hash_read_requests]]
[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.note_hash_read_requests]]
[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.note_hash_read_requests]]
[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.note_hash_read_requests]]
[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.note_hash_read_requests]]
[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.note_hash_read_requests]]
[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.note_hash_read_requests]]
[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.note_hash_read_requests]]
[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.note_hash_read_requests]]
[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.note_hash_read_requests]]
[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.note_hash_read_requests]]
[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.note_hash_read_requests]]
[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.note_hash_read_requests]]
[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.note_hash_read_requests]]
[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.note_hash_read_requests]]
[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.note_hash_read_requests]]
[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.note_hash_read_requests]]
[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.nullifier_read_requests]]
[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.nullifier_read_requests]]
[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.nullifier_read_requests]]
[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.nullifier_read_requests]]
[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.nullifier_read_requests]]
[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.nullifier_read_requests]]
[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.nullifier_read_requests]]
[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.nullifier_read_requests]]
[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.nullifier_read_requests]]
[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.nullifier_read_requests]]
[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.nullifier_read_requests]]
[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.nullifier_read_requests]]
[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.nullifier_read_requests]]
[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.nullifier_read_requests]]
[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.nullifier_read_requests]]
[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.nullifier_read_requests]]
[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.nullifier_read_requests]]
[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.nullifier_read_requests]]
[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.nullifier_read_requests]]
[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.nullifier_read_requests]]
[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.nullifier_read_requests]]
[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.nullifier_read_requests]]
[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.nullifier_read_requests]]
[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.nullifier_read_requests]]
[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.nullifier_read_requests]]
[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.nullifier_read_requests]]
[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.nullifier_read_requests]]
[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.nullifier_read_requests]]
[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.nullifier_read_requests]]
[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.nullifier_read_requests]]
[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.nullifier_read_requests]]
[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.nullifier_read_requests]]
[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.nullifier_read_requests]]
[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.nullifier_read_requests]]
[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.nullifier_read_requests]]
[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.nullifier_read_requests]]
[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.nullifier_read_requests]]
[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.nullifier_read_requests]]
[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.nullifier_read_requests]]
[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.nullifier_read_requests]]
[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.nullifier_read_requests]]
[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.nullifier_read_requests]]
[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.nullifier_read_requests]]
[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.nullifier_read_requests]]
[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.nullifier_read_requests]]
[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.nullifier_read_requests]]
[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.nullifier_read_requests]]
[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.nullifier_read_requests]]
[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.nullifier_read_requests]]
[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.nullifier_read_requests]]
[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.nullifier_read_requests]]
[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.nullifier_read_requests]]
[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.nullifier_read_requests]]
[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.nullifier_read_requests]]
[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.nullifier_read_requests]]
[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.nullifier_read_requests]]
[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.nullifier_read_requests]]
[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.nullifier_read_requests]]
[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.nullifier_read_requests]]
[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.nullifier_read_requests]]
[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.nullifier_read_requests]]
[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.nullifier_read_requests]]
[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.nullifier_read_requests]]
[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.nullifier_read_requests]]
[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators]]
[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request]
sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request]
  sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request.pk_m]
    x = "0x0000000000000000000000000000000000000000000000000000000000000000"
    y = "0x0000000000000000000000000000000000000000000000000000000000000000"
    is_infinite = false

[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators]]
[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request]
sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request]
  sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request.pk_m]
    x = "0x0000000000000000000000000000000000000000000000000000000000000000"
    y = "0x0000000000000000000000000000000000000000000000000000000000000000"
    is_infinite = false

[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators]]
[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request]
sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request]
  sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request.pk_m]
    x = "0x0000000000000000000000000000000000000000000000000000000000000000"
    y = "0x0000000000000000000000000000000000000000000000000000000000000000"
    is_infinite = false

[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators]]
[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request]
sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request]
  sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request.pk_m]
    x = "0x0000000000000000000000000000000000000000000000000000000000000000"
    y = "0x0000000000000000000000000000000000000000000000000000000000000000"
    is_infinite = false

[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators]]
[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request]
sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request]
  sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request.pk_m]
    x = "0x0000000000000000000000000000000000000000000000000000000000000000"
    y = "0x0000000000000000000000000000000000000000000000000000000000000000"
    is_infinite = false

[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators]]
[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request]
sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request]
  sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request.pk_m]
    x = "0x0000000000000000000000000000000000000000000000000000000000000000"
    y = "0x0000000000000000000000000000000000000000000000000000000000000000"
    is_infinite = false

[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators]]
[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request]
sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request]
  sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request.pk_m]
    x = "0x0000000000000000000000000000000000000000000000000000000000000000"
    y = "0x0000000000000000000000000000000000000000000000000000000000000000"
    is_infinite = false

[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators]]
[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request]
sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request]
  sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request.pk_m]
    x = "0x0000000000000000000000000000000000000000000000000000000000000000"
    y = "0x0000000000000000000000000000000000000000000000000000000000000000"
    is_infinite = false

[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators]]
[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request]
sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request]
  sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request.pk_m]
    x = "0x0000000000000000000000000000000000000000000000000000000000000000"
    y = "0x0000000000000000000000000000000000000000000000000000000000000000"
    is_infinite = false

[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators]]
[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request]
sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request]
  sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request.pk_m]
    x = "0x0000000000000000000000000000000000000000000000000000000000000000"
    y = "0x0000000000000000000000000000000000000000000000000000000000000000"
    is_infinite = false

[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators]]
[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request]
sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request]
  sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request.pk_m]
    x = "0x0000000000000000000000000000000000000000000000000000000000000000"
    y = "0x0000000000000000000000000000000000000000000000000000000000000000"
    is_infinite = false

[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators]]
[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request]
sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request]
  sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request.pk_m]
    x = "0x0000000000000000000000000000000000000000000000000000000000000000"
    y = "0x0000000000000000000000000000000000000000000000000000000000000000"
    is_infinite = false

[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators]]
[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request]
sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request]
  sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request.pk_m]
    x = "0x0000000000000000000000000000000000000000000000000000000000000000"
    y = "0x0000000000000000000000000000000000000000000000000000000000000000"
    is_infinite = false

[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators]]
[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request]
sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request]
  sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request.pk_m]
    x = "0x0000000000000000000000000000000000000000000000000000000000000000"
    y = "0x0000000000000000000000000000000000000000000000000000000000000000"
    is_infinite = false

[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators]]
[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request]
sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request]
  sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request.pk_m]
    x = "0x0000000000000000000000000000000000000000000000000000000000000000"
    y = "0x0000000000000000000000000000000000000000000000000000000000000000"
    is_infinite = false

[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators]]
[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request]
sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request]
  sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request.pk_m]
    x = "0x0000000000000000000000000000000000000000000000000000000000000000"
    y = "0x0000000000000000000000000000000000000000000000000000000000000000"
    is_infinite = false

[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators]]
[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request]
sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request]
  sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request.pk_m]
    x = "0x0000000000000000000000000000000000000000000000000000000000000000"
    y = "0x0000000000000000000000000000000000000000000000000000000000000000"
    is_infinite = false

[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators]]
[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request]
sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request]
  sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request.pk_m]
    x = "0x0000000000000000000000000000000000000000000000000000000000000000"
    y = "0x0000000000000000000000000000000000000000000000000000000000000000"
    is_infinite = false

[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators]]
[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request]
sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request]
  sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request.pk_m]
    x = "0x0000000000000000000000000000000000000000000000000000000000000000"
    y = "0x0000000000000000000000000000000000000000000000000000000000000000"
    is_infinite = false

[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators]]
[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request]
sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request]
  sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request.pk_m]
    x = "0x0000000000000000000000000000000000000000000000000000000000000000"
    y = "0x0000000000000000000000000000000000000000000000000000000000000000"
    is_infinite = false

[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators]]
[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request]
sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request]
  sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request.pk_m]
    x = "0x0000000000000000000000000000000000000000000000000000000000000000"
    y = "0x0000000000000000000000000000000000000000000000000000000000000000"
    is_infinite = false

[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators]]
[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request]
sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request]
  sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request.pk_m]
    x = "0x0000000000000000000000000000000000000000000000000000000000000000"
    y = "0x0000000000000000000000000000000000000000000000000000000000000000"
    is_infinite = false

[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators]]
[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request]
sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request]
  sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request.pk_m]
    x = "0x0000000000000000000000000000000000000000000000000000000000000000"
    y = "0x0000000000000000000000000000000000000000000000000000000000000000"
    is_infinite = false

[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators]]
[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request]
sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request]
  sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request.pk_m]
    x = "0x0000000000000000000000000000000000000000000000000000000000000000"
    y = "0x0000000000000000000000000000000000000000000000000000000000000000"
    is_infinite = false

[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators]]
[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request]
sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request]
  sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request.pk_m]
    x = "0x0000000000000000000000000000000000000000000000000000000000000000"
    y = "0x0000000000000000000000000000000000000000000000000000000000000000"
    is_infinite = false

[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators]]
[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request]
sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request]
  sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request.pk_m]
    x = "0x0000000000000000000000000000000000000000000000000000000000000000"
    y = "0x0000000000000000000000000000000000000000000000000000000000000000"
    is_infinite = false

[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators]]
[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request]
sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request]
  sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request.pk_m]
    x = "0x0000000000000000000000000000000000000000000000000000000000000000"
    y = "0x0000000000000000000000000000000000000000000000000000000000000000"
    is_infinite = false

[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators]]
[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request]
sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request]
  sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request.pk_m]
    x = "0x0000000000000000000000000000000000000000000000000000000000000000"
    y = "0x0000000000000000000000000000000000000000000000000000000000000000"
    is_infinite = false

[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators]]
[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request]
sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request]
  sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request.pk_m]
    x = "0x0000000000000000000000000000000000000000000000000000000000000000"
    y = "0x0000000000000000000000000000000000000000000000000000000000000000"
    is_infinite = false

[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators]]
[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request]
sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request]
  sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request.pk_m]
    x = "0x0000000000000000000000000000000000000000000000000000000000000000"
    y = "0x0000000000000000000000000000000000000000000000000000000000000000"
    is_infinite = false

[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators]]
[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request]
sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request]
  sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request.pk_m]
    x = "0x0000000000000000000000000000000000000000000000000000000000000000"
    y = "0x0000000000000000000000000000000000000000000000000000000000000000"
    is_infinite = false

[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators]]
[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request]
sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request]
  sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request.pk_m]
    x = "0x0000000000000000000000000000000000000000000000000000000000000000"
    y = "0x0000000000000000000000000000000000000000000000000000000000000000"
    is_infinite = false

[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators]]
[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request]
sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request]
  sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request.pk_m]
    x = "0x0000000000000000000000000000000000000000000000000000000000000000"
    y = "0x0000000000000000000000000000000000000000000000000000000000000000"
    is_infinite = false

[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators]]
[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request]
sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request]
  sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request.pk_m]
    x = "0x0000000000000000000000000000000000000000000000000000000000000000"
    y = "0x0000000000000000000000000000000000000000000000000000000000000000"
    is_infinite = false

[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators]]
[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request]
sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request]
  sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request.pk_m]
    x = "0x0000000000000000000000000000000000000000000000000000000000000000"
    y = "0x0000000000000000000000000000000000000000000000000000000000000000"
    is_infinite = false

[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators]]
[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request]
sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request]
  sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request.pk_m]
    x = "0x0000000000000000000000000000000000000000000000000000000000000000"
    y = "0x0000000000000000000000000000000000000000000000000000000000000000"
    is_infinite = false

[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators]]
[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request]
sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request]
  sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request.pk_m]
    x = "0x0000000000000000000000000000000000000000000000000000000000000000"
    y = "0x0000000000000000000000000000000000000000000000000000000000000000"
    is_infinite = false

[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators]]
[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request]
sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request]
  sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request.pk_m]
    x = "0x0000000000000000000000000000000000000000000000000000000000000000"
    y = "0x0000000000000000000000000000000000000000000000000000000000000000"
    is_infinite = false

[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators]]
[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request]
sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request]
  sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request.pk_m]
    x = "0x0000000000000000000000000000000000000000000000000000000000000000"
    y = "0x0000000000000000000000000000000000000000000000000000000000000000"
    is_infinite = false

[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators]]
[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request]
sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request]
  sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request.pk_m]
    x = "0x0000000000000000000000000000000000000000000000000000000000000000"
    y = "0x0000000000000000000000000000000000000000000000000000000000000000"
    is_infinite = false

[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators]]
[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request]
sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request]
  sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request.pk_m]
    x = "0x0000000000000000000000000000000000000000000000000000000000000000"
    y = "0x0000000000000000000000000000000000000000000000000000000000000000"
    is_infinite = false

[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators]]
[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request]
sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request]
  sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request.pk_m]
    x = "0x0000000000000000000000000000000000000000000000000000000000000000"
    y = "0x0000000000000000000000000000000000000000000000000000000000000000"
    is_infinite = false

[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators]]
[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request]
sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request]
  sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request.pk_m]
    x = "0x0000000000000000000000000000000000000000000000000000000000000000"
    y = "0x0000000000000000000000000000000000000000000000000000000000000000"
    is_infinite = false

[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators]]
[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request]
sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request]
  sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request.pk_m]
    x = "0x0000000000000000000000000000000000000000000000000000000000000000"
    y = "0x0000000000000000000000000000000000000000000000000000000000000000"
    is_infinite = false

[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators]]
[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request]
sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request]
  sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request.pk_m]
    x = "0x0000000000000000000000000000000000000000000000000000000000000000"
    y = "0x0000000000000000000000000000000000000000000000000000000000000000"
    is_infinite = false

[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators]]
[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request]
sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request]
  sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request.pk_m]
    x = "0x0000000000000000000000000000000000000000000000000000000000000000"
    y = "0x0000000000000000000000000000000000000000000000000000000000000000"
    is_infinite = false

[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators]]
[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request]
sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request]
  sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request.pk_m]
    x = "0x0000000000000000000000000000000000000000000000000000000000000000"
    y = "0x0000000000000000000000000000000000000000000000000000000000000000"
    is_infinite = false

[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators]]
[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request]
sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request]
  sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request.pk_m]
    x = "0x0000000000000000000000000000000000000000000000000000000000000000"
    y = "0x0000000000000000000000000000000000000000000000000000000000000000"
    is_infinite = false

[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators]]
[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request]
sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request]
  sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request.pk_m]
    x = "0x0000000000000000000000000000000000000000000000000000000000000000"
    y = "0x0000000000000000000000000000000000000000000000000000000000000000"
    is_infinite = false

[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators]]
[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request]
sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request]
  sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request.pk_m]
    x = "0x0000000000000000000000000000000000000000000000000000000000000000"
    y = "0x0000000000000000000000000000000000000000000000000000000000000000"
    is_infinite = false

[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators]]
[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request]
sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request]
  sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request.pk_m]
    x = "0x0000000000000000000000000000000000000000000000000000000000000000"
    y = "0x0000000000000000000000000000000000000000000000000000000000000000"
    is_infinite = false

[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators]]
[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request]
sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request]
  sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request.pk_m]
    x = "0x0000000000000000000000000000000000000000000000000000000000000000"
    y = "0x0000000000000000000000000000000000000000000000000000000000000000"
    is_infinite = false

[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators]]
[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request]
sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request]
  sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request.pk_m]
    x = "0x0000000000000000000000000000000000000000000000000000000000000000"
    y = "0x0000000000000000000000000000000000000000000000000000000000000000"
    is_infinite = false

[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators]]
[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request]
sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request]
  sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request.pk_m]
    x = "0x0000000000000000000000000000000000000000000000000000000000000000"
    y = "0x0000000000000000000000000000000000000000000000000000000000000000"
    is_infinite = false

[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators]]
[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request]
sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request]
  sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request.pk_m]
    x = "0x0000000000000000000000000000000000000000000000000000000000000000"
    y = "0x0000000000000000000000000000000000000000000000000000000000000000"
    is_infinite = false

[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators]]
[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request]
sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request]
  sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request.pk_m]
    x = "0x0000000000000000000000000000000000000000000000000000000000000000"
    y = "0x0000000000000000000000000000000000000000000000000000000000000000"
    is_infinite = false

[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators]]
[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request]
sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request]
  sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request.pk_m]
    x = "0x0000000000000000000000000000000000000000000000000000000000000000"
    y = "0x0000000000000000000000000000000000000000000000000000000000000000"
    is_infinite = false

[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators]]
[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request]
sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request]
  sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request.pk_m]
    x = "0x0000000000000000000000000000000000000000000000000000000000000000"
    y = "0x0000000000000000000000000000000000000000000000000000000000000000"
    is_infinite = false

[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators]]
[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request]
sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request]
  sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request.pk_m]
    x = "0x0000000000000000000000000000000000000000000000000000000000000000"
    y = "0x0000000000000000000000000000000000000000000000000000000000000000"
    is_infinite = false

[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators]]
[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request]
sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request]
  sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request.pk_m]
    x = "0x0000000000000000000000000000000000000000000000000000000000000000"
    y = "0x0000000000000000000000000000000000000000000000000000000000000000"
    is_infinite = false

[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators]]
[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request]
sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request]
  sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request.pk_m]
    x = "0x0000000000000000000000000000000000000000000000000000000000000000"
    y = "0x0000000000000000000000000000000000000000000000000000000000000000"
    is_infinite = false

[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators]]
[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request]
sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request]
  sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request.pk_m]
    x = "0x0000000000000000000000000000000000000000000000000000000000000000"
    y = "0x0000000000000000000000000000000000000000000000000000000000000000"
    is_infinite = false

[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators]]
[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request]
sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request]
  sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request.pk_m]
    x = "0x0000000000000000000000000000000000000000000000000000000000000000"
    y = "0x0000000000000000000000000000000000000000000000000000000000000000"
    is_infinite = false

[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators]]
[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request]
sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request]
  sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request.pk_m]
    x = "0x0000000000000000000000000000000000000000000000000000000000000000"
    y = "0x0000000000000000000000000000000000000000000000000000000000000000"
    is_infinite = false

[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.split_counter]
_is_some = false
_value = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.note_hashes]]
[previous_kernel_public_inputs.end.note_hashes.note_hash]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.note_hashes.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.note_hashes]]
[previous_kernel_public_inputs.end.note_hashes.note_hash]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.note_hashes.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.note_hashes]]
[previous_kernel_public_inputs.end.note_hashes.note_hash]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.note_hashes.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.note_hashes]]
[previous_kernel_public_inputs.end.note_hashes.note_hash]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.note_hashes.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.note_hashes]]
[previous_kernel_public_inputs.end.note_hashes.note_hash]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.note_hashes.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.note_hashes]]
[previous_kernel_public_inputs.end.note_hashes.note_hash]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.note_hashes.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.note_hashes]]
[previous_kernel_public_inputs.end.note_hashes.note_hash]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.note_hashes.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.note_hashes]]
[previous_kernel_public_inputs.end.note_hashes.note_hash]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.note_hashes.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.note_hashes]]
[previous_kernel_public_inputs.end.note_hashes.note_hash]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.note_hashes.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.note_hashes]]
[previous_kernel_public_inputs.end.note_hashes.note_hash]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.note_hashes.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.note_hashes]]
[previous_kernel_public_inputs.end.note_hashes.note_hash]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.note_hashes.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.note_hashes]]
[previous_kernel_public_inputs.end.note_hashes.note_hash]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.note_hashes.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.note_hashes]]
[previous_kernel_public_inputs.end.note_hashes.note_hash]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.note_hashes.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.note_hashes]]
[previous_kernel_public_inputs.end.note_hashes.note_hash]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.note_hashes.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.note_hashes]]
[previous_kernel_public_inputs.end.note_hashes.note_hash]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.note_hashes.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.note_hashes]]
[previous_kernel_public_inputs.end.note_hashes.note_hash]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.note_hashes.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.note_hashes]]
[previous_kernel_public_inputs.end.note_hashes.note_hash]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.note_hashes.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.note_hashes]]
[previous_kernel_public_inputs.end.note_hashes.note_hash]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.note_hashes.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.note_hashes]]
[previous_kernel_public_inputs.end.note_hashes.note_hash]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.note_hashes.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.note_hashes]]
[previous_kernel_public_inputs.end.note_hashes.note_hash]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.note_hashes.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.note_hashes]]
[previous_kernel_public_inputs.end.note_hashes.note_hash]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.note_hashes.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.note_hashes]]
[previous_kernel_public_inputs.end.note_hashes.note_hash]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.note_hashes.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.note_hashes]]
[previous_kernel_public_inputs.end.note_hashes.note_hash]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.note_hashes.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.note_hashes]]
[previous_kernel_public_inputs.end.note_hashes.note_hash]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.note_hashes.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.note_hashes]]
[previous_kernel_public_inputs.end.note_hashes.note_hash]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.note_hashes.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.note_hashes]]
[previous_kernel_public_inputs.end.note_hashes.note_hash]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.note_hashes.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.note_hashes]]
[previous_kernel_public_inputs.end.note_hashes.note_hash]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.note_hashes.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.note_hashes]]
[previous_kernel_public_inputs.end.note_hashes.note_hash]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.note_hashes.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.note_hashes]]
[previous_kernel_public_inputs.end.note_hashes.note_hash]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.note_hashes.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.note_hashes]]
[previous_kernel_public_inputs.end.note_hashes.note_hash]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.note_hashes.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.note_hashes]]
[previous_kernel_public_inputs.end.note_hashes.note_hash]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.note_hashes.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.note_hashes]]
[previous_kernel_public_inputs.end.note_hashes.note_hash]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.note_hashes.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.note_hashes]]
[previous_kernel_public_inputs.end.note_hashes.note_hash]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.note_hashes.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.note_hashes]]
[previous_kernel_public_inputs.end.note_hashes.note_hash]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.note_hashes.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.note_hashes]]
[previous_kernel_public_inputs.end.note_hashes.note_hash]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.note_hashes.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.note_hashes]]
[previous_kernel_public_inputs.end.note_hashes.note_hash]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.note_hashes.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.note_hashes]]
[previous_kernel_public_inputs.end.note_hashes.note_hash]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.note_hashes.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.note_hashes]]
[previous_kernel_public_inputs.end.note_hashes.note_hash]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.note_hashes.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.note_hashes]]
[previous_kernel_public_inputs.end.note_hashes.note_hash]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.note_hashes.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.note_hashes]]
[previous_kernel_public_inputs.end.note_hashes.note_hash]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.note_hashes.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.note_hashes]]
[previous_kernel_public_inputs.end.note_hashes.note_hash]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.note_hashes.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.note_hashes]]
[previous_kernel_public_inputs.end.note_hashes.note_hash]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.note_hashes.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.note_hashes]]
[previous_kernel_public_inputs.end.note_hashes.note_hash]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.note_hashes.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.note_hashes]]
[previous_kernel_public_inputs.end.note_hashes.note_hash]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.note_hashes.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.note_hashes]]
[previous_kernel_public_inputs.end.note_hashes.note_hash]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.note_hashes.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.note_hashes]]
[previous_kernel_public_inputs.end.note_hashes.note_hash]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.note_hashes.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.note_hashes]]
[previous_kernel_public_inputs.end.note_hashes.note_hash]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.note_hashes.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.note_hashes]]
[previous_kernel_public_inputs.end.note_hashes.note_hash]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.note_hashes.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.note_hashes]]
[previous_kernel_public_inputs.end.note_hashes.note_hash]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.note_hashes.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.note_hashes]]
[previous_kernel_public_inputs.end.note_hashes.note_hash]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.note_hashes.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.note_hashes]]
[previous_kernel_public_inputs.end.note_hashes.note_hash]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.note_hashes.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.note_hashes]]
[previous_kernel_public_inputs.end.note_hashes.note_hash]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.note_hashes.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.note_hashes]]
[previous_kernel_public_inputs.end.note_hashes.note_hash]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.note_hashes.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.note_hashes]]
[previous_kernel_public_inputs.end.note_hashes.note_hash]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.note_hashes.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.note_hashes]]
[previous_kernel_public_inputs.end.note_hashes.note_hash]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.note_hashes.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.note_hashes]]
[previous_kernel_public_inputs.end.note_hashes.note_hash]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.note_hashes.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.note_hashes]]
[previous_kernel_public_inputs.end.note_hashes.note_hash]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.note_hashes.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.note_hashes]]
[previous_kernel_public_inputs.end.note_hashes.note_hash]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.note_hashes.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.note_hashes]]
[previous_kernel_public_inputs.end.note_hashes.note_hash]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.note_hashes.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.note_hashes]]
[previous_kernel_public_inputs.end.note_hashes.note_hash]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.note_hashes.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.note_hashes]]
[previous_kernel_public_inputs.end.note_hashes.note_hash]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.note_hashes.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.note_hashes]]
[previous_kernel_public_inputs.end.note_hashes.note_hash]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.note_hashes.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.note_hashes]]
[previous_kernel_public_inputs.end.note_hashes.note_hash]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.note_hashes.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.note_hashes]]
[previous_kernel_public_inputs.end.note_hashes.note_hash]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.note_hashes.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.nullifiers]]
[previous_kernel_public_inputs.end.nullifiers.nullifier]
value = "0x27e956c18dc1225871b3e6e277ea0c9d30058e095e4818da55052f7c994bfe8e"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.nullifiers.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.nullifiers]]
[previous_kernel_public_inputs.end.nullifiers.nullifier]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.nullifiers.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.nullifiers]]
[previous_kernel_public_inputs.end.nullifiers.nullifier]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.nullifiers.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.nullifiers]]
[previous_kernel_public_inputs.end.nullifiers.nullifier]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.nullifiers.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.nullifiers]]
[previous_kernel_public_inputs.end.nullifiers.nullifier]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.nullifiers.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.nullifiers]]
[previous_kernel_public_inputs.end.nullifiers.nullifier]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.nullifiers.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.nullifiers]]
[previous_kernel_public_inputs.end.nullifiers.nullifier]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.nullifiers.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.nullifiers]]
[previous_kernel_public_inputs.end.nullifiers.nullifier]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.nullifiers.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.nullifiers]]
[previous_kernel_public_inputs.end.nullifiers.nullifier]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.nullifiers.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.nullifiers]]
[previous_kernel_public_inputs.end.nullifiers.nullifier]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.nullifiers.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.nullifiers]]
[previous_kernel_public_inputs.end.nullifiers.nullifier]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.nullifiers.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.nullifiers]]
[previous_kernel_public_inputs.end.nullifiers.nullifier]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.nullifiers.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.nullifiers]]
[previous_kernel_public_inputs.end.nullifiers.nullifier]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.nullifiers.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.nullifiers]]
[previous_kernel_public_inputs.end.nullifiers.nullifier]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.nullifiers.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.nullifiers]]
[previous_kernel_public_inputs.end.nullifiers.nullifier]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.nullifiers.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.nullifiers]]
[previous_kernel_public_inputs.end.nullifiers.nullifier]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.nullifiers.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.nullifiers]]
[previous_kernel_public_inputs.end.nullifiers.nullifier]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.nullifiers.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.nullifiers]]
[previous_kernel_public_inputs.end.nullifiers.nullifier]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.nullifiers.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.nullifiers]]
[previous_kernel_public_inputs.end.nullifiers.nullifier]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.nullifiers.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.nullifiers]]
[previous_kernel_public_inputs.end.nullifiers.nullifier]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.nullifiers.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.nullifiers]]
[previous_kernel_public_inputs.end.nullifiers.nullifier]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.nullifiers.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.nullifiers]]
[previous_kernel_public_inputs.end.nullifiers.nullifier]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.nullifiers.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.nullifiers]]
[previous_kernel_public_inputs.end.nullifiers.nullifier]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.nullifiers.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.nullifiers]]
[previous_kernel_public_inputs.end.nullifiers.nullifier]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.nullifiers.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.nullifiers]]
[previous_kernel_public_inputs.end.nullifiers.nullifier]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.nullifiers.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.nullifiers]]
[previous_kernel_public_inputs.end.nullifiers.nullifier]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.nullifiers.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.nullifiers]]
[previous_kernel_public_inputs.end.nullifiers.nullifier]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.nullifiers.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.nullifiers]]
[previous_kernel_public_inputs.end.nullifiers.nullifier]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.nullifiers.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.nullifiers]]
[previous_kernel_public_inputs.end.nullifiers.nullifier]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.nullifiers.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.nullifiers]]
[previous_kernel_public_inputs.end.nullifiers.nullifier]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.nullifiers.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.nullifiers]]
[previous_kernel_public_inputs.end.nullifiers.nullifier]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.nullifiers.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.nullifiers]]
[previous_kernel_public_inputs.end.nullifiers.nullifier]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.nullifiers.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.nullifiers]]
[previous_kernel_public_inputs.end.nullifiers.nullifier]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.nullifiers.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.nullifiers]]
[previous_kernel_public_inputs.end.nullifiers.nullifier]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.nullifiers.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.nullifiers]]
[previous_kernel_public_inputs.end.nullifiers.nullifier]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.nullifiers.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.nullifiers]]
[previous_kernel_public_inputs.end.nullifiers.nullifier]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.nullifiers.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.nullifiers]]
[previous_kernel_public_inputs.end.nullifiers.nullifier]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.nullifiers.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.nullifiers]]
[previous_kernel_public_inputs.end.nullifiers.nullifier]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.nullifiers.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.nullifiers]]
[previous_kernel_public_inputs.end.nullifiers.nullifier]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.nullifiers.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.nullifiers]]
[previous_kernel_public_inputs.end.nullifiers.nullifier]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.nullifiers.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.nullifiers]]
[previous_kernel_public_inputs.end.nullifiers.nullifier]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.nullifiers.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.nullifiers]]
[previous_kernel_public_inputs.end.nullifiers.nullifier]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.nullifiers.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.nullifiers]]
[previous_kernel_public_inputs.end.nullifiers.nullifier]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.nullifiers.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.nullifiers]]
[previous_kernel_public_inputs.end.nullifiers.nullifier]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.nullifiers.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.nullifiers]]
[previous_kernel_public_inputs.end.nullifiers.nullifier]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.nullifiers.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.nullifiers]]
[previous_kernel_public_inputs.end.nullifiers.nullifier]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.nullifiers.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.nullifiers]]
[previous_kernel_public_inputs.end.nullifiers.nullifier]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.nullifiers.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.nullifiers]]
[previous_kernel_public_inputs.end.nullifiers.nullifier]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.nullifiers.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.nullifiers]]
[previous_kernel_public_inputs.end.nullifiers.nullifier]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.nullifiers.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.nullifiers]]
[previous_kernel_public_inputs.end.nullifiers.nullifier]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.nullifiers.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.nullifiers]]
[previous_kernel_public_inputs.end.nullifiers.nullifier]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.nullifiers.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.nullifiers]]
[previous_kernel_public_inputs.end.nullifiers.nullifier]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.nullifiers.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.nullifiers]]
[previous_kernel_public_inputs.end.nullifiers.nullifier]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.nullifiers.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.nullifiers]]
[previous_kernel_public_inputs.end.nullifiers.nullifier]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.nullifiers.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.nullifiers]]
[previous_kernel_public_inputs.end.nullifiers.nullifier]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.nullifiers.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.nullifiers]]
[previous_kernel_public_inputs.end.nullifiers.nullifier]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.nullifiers.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.nullifiers]]
[previous_kernel_public_inputs.end.nullifiers.nullifier]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.nullifiers.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.nullifiers]]
[previous_kernel_public_inputs.end.nullifiers.nullifier]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.nullifiers.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.nullifiers]]
[previous_kernel_public_inputs.end.nullifiers.nullifier]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.nullifiers.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.nullifiers]]
[previous_kernel_public_inputs.end.nullifiers.nullifier]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.nullifiers.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.nullifiers]]
[previous_kernel_public_inputs.end.nullifiers.nullifier]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.nullifiers.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.nullifiers]]
[previous_kernel_public_inputs.end.nullifiers.nullifier]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.nullifiers.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.nullifiers]]
[previous_kernel_public_inputs.end.nullifiers.nullifier]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.nullifiers.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.nullifiers]]
[previous_kernel_public_inputs.end.nullifiers.nullifier]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.nullifiers.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.l2_to_l1_msgs]]
[previous_kernel_public_inputs.end.l2_to_l1_msgs.inner]
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.l2_to_l1_msgs.inner.inner]
  content = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.l2_to_l1_msgs.inner.inner.recipient]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.l2_to_l1_msgs.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.l2_to_l1_msgs]]
[previous_kernel_public_inputs.end.l2_to_l1_msgs.inner]
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.l2_to_l1_msgs.inner.inner]
  content = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.l2_to_l1_msgs.inner.inner.recipient]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.l2_to_l1_msgs.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.l2_to_l1_msgs]]
[previous_kernel_public_inputs.end.l2_to_l1_msgs.inner]
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.l2_to_l1_msgs.inner.inner]
  content = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.l2_to_l1_msgs.inner.inner.recipient]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.l2_to_l1_msgs.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.l2_to_l1_msgs]]
[previous_kernel_public_inputs.end.l2_to_l1_msgs.inner]
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.l2_to_l1_msgs.inner.inner]
  content = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.l2_to_l1_msgs.inner.inner.recipient]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.l2_to_l1_msgs.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.l2_to_l1_msgs]]
[previous_kernel_public_inputs.end.l2_to_l1_msgs.inner]
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.l2_to_l1_msgs.inner.inner]
  content = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.l2_to_l1_msgs.inner.inner.recipient]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.l2_to_l1_msgs.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.l2_to_l1_msgs]]
[previous_kernel_public_inputs.end.l2_to_l1_msgs.inner]
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.l2_to_l1_msgs.inner.inner]
  content = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.l2_to_l1_msgs.inner.inner.recipient]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.l2_to_l1_msgs.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.l2_to_l1_msgs]]
[previous_kernel_public_inputs.end.l2_to_l1_msgs.inner]
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.l2_to_l1_msgs.inner.inner]
  content = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.l2_to_l1_msgs.inner.inner.recipient]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.l2_to_l1_msgs.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.l2_to_l1_msgs]]
[previous_kernel_public_inputs.end.l2_to_l1_msgs.inner]
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.l2_to_l1_msgs.inner.inner]
  content = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.l2_to_l1_msgs.inner.inner.recipient]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.l2_to_l1_msgs.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.private_logs]]
[previous_kernel_public_inputs.end.private_logs.inner]
note_hash_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.private_logs.inner.log]
  fields = [
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000"
]
  length = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.private_logs.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.private_logs]]
[previous_kernel_public_inputs.end.private_logs.inner]
note_hash_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.private_logs.inner.log]
  fields = [
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000"
]
  length = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.private_logs.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.private_logs]]
[previous_kernel_public_inputs.end.private_logs.inner]
note_hash_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.private_logs.inner.log]
  fields = [
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000"
]
  length = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.private_logs.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.private_logs]]
[previous_kernel_public_inputs.end.private_logs.inner]
note_hash_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.private_logs.inner.log]
  fields = [
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000"
]
  length = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.private_logs.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.private_logs]]
[previous_kernel_public_inputs.end.private_logs.inner]
note_hash_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.private_logs.inner.log]
  fields = [
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000"
]
  length = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.private_logs.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.private_logs]]
[previous_kernel_public_inputs.end.private_logs.inner]
note_hash_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.private_logs.inner.log]
  fields = [
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000"
]
  length = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.private_logs.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.private_logs]]
[previous_kernel_public_inputs.end.private_logs.inner]
note_hash_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.private_logs.inner.log]
  fields = [
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000"
]
  length = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.private_logs.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.private_logs]]
[previous_kernel_public_inputs.end.private_logs.inner]
note_hash_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.private_logs.inner.log]
  fields = [
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000"
]
  length = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.private_logs.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.private_logs]]
[previous_kernel_public_inputs.end.private_logs.inner]
note_hash_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.private_logs.inner.log]
  fields = [
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000"
]
  length = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.private_logs.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.private_logs]]
[previous_kernel_public_inputs.end.private_logs.inner]
note_hash_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.private_logs.inner.log]
  fields = [
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000"
]
  length = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.private_logs.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.private_logs]]
[previous_kernel_public_inputs.end.private_logs.inner]
note_hash_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.private_logs.inner.log]
  fields = [
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000"
]
  length = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.private_logs.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.private_logs]]
[previous_kernel_public_inputs.end.private_logs.inner]
note_hash_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.private_logs.inner.log]
  fields = [
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000"
]
  length = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.private_logs.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.private_logs]]
[previous_kernel_public_inputs.end.private_logs.inner]
note_hash_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.private_logs.inner.log]
  fields = [
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000"
]
  length = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.private_logs.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.private_logs]]
[previous_kernel_public_inputs.end.private_logs.inner]
note_hash_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.private_logs.inner.log]
  fields = [
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000"
]
  length = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.private_logs.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.private_logs]]
[previous_kernel_public_inputs.end.private_logs.inner]
note_hash_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.private_logs.inner.log]
  fields = [
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000"
]
  length = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.private_logs.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.private_logs]]
[previous_kernel_public_inputs.end.private_logs.inner]
note_hash_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.private_logs.inner.log]
  fields = [
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000"
]
  length = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.private_logs.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.private_logs]]
[previous_kernel_public_inputs.end.private_logs.inner]
note_hash_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.private_logs.inner.log]
  fields = [
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000"
]
  length = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.private_logs.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.private_logs]]
[previous_kernel_public_inputs.end.private_logs.inner]
note_hash_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.private_logs.inner.log]
  fields = [
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000"
]
  length = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.private_logs.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.private_logs]]
[previous_kernel_public_inputs.end.private_logs.inner]
note_hash_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.private_logs.inner.log]
  fields = [
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000"
]
  length = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.private_logs.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.private_logs]]
[previous_kernel_public_inputs.end.private_logs.inner]
note_hash_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.private_logs.inner.log]
  fields = [
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000"
]
  length = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.private_logs.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.private_logs]]
[previous_kernel_public_inputs.end.private_logs.inner]
note_hash_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.private_logs.inner.log]
  fields = [
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000"
]
  length = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.private_logs.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.private_logs]]
[previous_kernel_public_inputs.end.private_logs.inner]
note_hash_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.private_logs.inner.log]
  fields = [
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000"
]
  length = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.private_logs.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.private_logs]]
[previous_kernel_public_inputs.end.private_logs.inner]
note_hash_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.private_logs.inner.log]
  fields = [
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000"
]
  length = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.private_logs.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.private_logs]]
[previous_kernel_public_inputs.end.private_logs.inner]
note_hash_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.private_logs.inner.log]
  fields = [
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000"
]
  length = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.private_logs.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.private_logs]]
[previous_kernel_public_inputs.end.private_logs.inner]
note_hash_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.private_logs.inner.log]
  fields = [
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000"
]
  length = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.private_logs.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.private_logs]]
[previous_kernel_public_inputs.end.private_logs.inner]
note_hash_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.private_logs.inner.log]
  fields = [
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000"
]
  length = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.private_logs.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.private_logs]]
[previous_kernel_public_inputs.end.private_logs.inner]
note_hash_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.private_logs.inner.log]
  fields = [
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000"
]
  length = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.private_logs.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.private_logs]]
[previous_kernel_public_inputs.end.private_logs.inner]
note_hash_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.private_logs.inner.log]
  fields = [
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000"
]
  length = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.private_logs.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.private_logs]]
[previous_kernel_public_inputs.end.private_logs.inner]
note_hash_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.private_logs.inner.log]
  fields = [
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000"
]
  length = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.private_logs.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.private_logs]]
[previous_kernel_public_inputs.end.private_logs.inner]
note_hash_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.private_logs.inner.log]
  fields = [
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000"
]
  length = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.private_logs.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.private_logs]]
[previous_kernel_public_inputs.end.private_logs.inner]
note_hash_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.private_logs.inner.log]
  fields = [
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000"
]
  length = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.private_logs.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.private_logs]]
[previous_kernel_public_inputs.end.private_logs.inner]
note_hash_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.private_logs.inner.log]
  fields = [
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000"
]
  length = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.private_logs.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.contract_class_logs_hashes]]
[previous_kernel_public_inputs.end.contract_class_logs_hashes.inner]
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.contract_class_logs_hashes.inner.inner]
  value = "0x0000000000000000000000000000000000000000000000000000000000000000"
  length = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.contract_class_logs_hashes.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.public_call_requests]]
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.public_call_requests.inner]
  is_static_call = false
  calldata_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.public_call_requests.inner.msg_sender]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.public_call_requests.inner.contract_address]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.public_call_requests]]
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.public_call_requests.inner]
  is_static_call = false
  calldata_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.public_call_requests.inner.msg_sender]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.public_call_requests.inner.contract_address]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.public_call_requests]]
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.public_call_requests.inner]
  is_static_call = false
  calldata_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.public_call_requests.inner.msg_sender]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.public_call_requests.inner.contract_address]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.public_call_requests]]
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.public_call_requests.inner]
  is_static_call = false
  calldata_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.public_call_requests.inner.msg_sender]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.public_call_requests.inner.contract_address]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.public_call_requests]]
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.public_call_requests.inner]
  is_static_call = false
  calldata_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.public_call_requests.inner.msg_sender]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.public_call_requests.inner.contract_address]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.public_call_requests]]
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.public_call_requests.inner]
  is_static_call = false
  calldata_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.public_call_requests.inner.msg_sender]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.public_call_requests.inner.contract_address]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.public_call_requests]]
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.public_call_requests.inner]
  is_static_call = false
  calldata_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.public_call_requests.inner.msg_sender]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.public_call_requests.inner.contract_address]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.public_call_requests]]
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.public_call_requests.inner]
  is_static_call = false
  calldata_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.public_call_requests.inner.msg_sender]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.public_call_requests.inner.contract_address]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.public_call_requests]]
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.public_call_requests.inner]
  is_static_call = false
  calldata_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.public_call_requests.inner.msg_sender]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.public_call_requests.inner.contract_address]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.public_call_requests]]
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.public_call_requests.inner]
  is_static_call = false
  calldata_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.public_call_requests.inner.msg_sender]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.public_call_requests.inner.contract_address]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.public_call_requests]]
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.public_call_requests.inner]
  is_static_call = false
  calldata_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.public_call_requests.inner.msg_sender]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.public_call_requests.inner.contract_address]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.public_call_requests]]
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.public_call_requests.inner]
  is_static_call = false
  calldata_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.public_call_requests.inner.msg_sender]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.public_call_requests.inner.contract_address]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.public_call_requests]]
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.public_call_requests.inner]
  is_static_call = false
  calldata_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.public_call_requests.inner.msg_sender]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.public_call_requests.inner.contract_address]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.public_call_requests]]
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.public_call_requests.inner]
  is_static_call = false
  calldata_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.public_call_requests.inner.msg_sender]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.public_call_requests.inner.contract_address]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.public_call_requests]]
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.public_call_requests.inner]
  is_static_call = false
  calldata_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.public_call_requests.inner.msg_sender]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.public_call_requests.inner.contract_address]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.public_call_requests]]
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.public_call_requests.inner]
  is_static_call = false
  calldata_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.public_call_requests.inner.msg_sender]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.public_call_requests.inner.contract_address]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.public_call_requests]]
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.public_call_requests.inner]
  is_static_call = false
  calldata_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.public_call_requests.inner.msg_sender]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.public_call_requests.inner.contract_address]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.public_call_requests]]
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.public_call_requests.inner]
  is_static_call = false
  calldata_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.public_call_requests.inner.msg_sender]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.public_call_requests.inner.contract_address]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.public_call_requests]]
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.public_call_requests.inner]
  is_static_call = false
  calldata_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.public_call_requests.inner.msg_sender]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.public_call_requests.inner.contract_address]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.public_call_requests]]
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.public_call_requests.inner]
  is_static_call = false
  calldata_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.public_call_requests.inner.msg_sender]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.public_call_requests.inner.contract_address]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.public_call_requests]]
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.public_call_requests.inner]
  is_static_call = false
  calldata_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.public_call_requests.inner.msg_sender]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.public_call_requests.inner.contract_address]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.public_call_requests]]
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.public_call_requests.inner]
  is_static_call = false
  calldata_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.public_call_requests.inner.msg_sender]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.public_call_requests.inner.contract_address]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.public_call_requests]]
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.public_call_requests.inner]
  is_static_call = false
  calldata_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.public_call_requests.inner.msg_sender]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.public_call_requests.inner.contract_address]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.public_call_requests]]
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.public_call_requests.inner]
  is_static_call = false
  calldata_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.public_call_requests.inner.msg_sender]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.public_call_requests.inner.contract_address]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.public_call_requests]]
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.public_call_requests.inner]
  is_static_call = false
  calldata_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.public_call_requests.inner.msg_sender]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.public_call_requests.inner.contract_address]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.public_call_requests]]
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.public_call_requests.inner]
  is_static_call = false
  calldata_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.public_call_requests.inner.msg_sender]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.public_call_requests.inner.contract_address]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.public_call_requests]]
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.public_call_requests.inner]
  is_static_call = false
  calldata_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.public_call_requests.inner.msg_sender]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.public_call_requests.inner.contract_address]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.public_call_requests]]
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.public_call_requests.inner]
  is_static_call = false
  calldata_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.public_call_requests.inner.msg_sender]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.public_call_requests.inner.contract_address]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.public_call_requests]]
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.public_call_requests.inner]
  is_static_call = false
  calldata_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.public_call_requests.inner.msg_sender]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.public_call_requests.inner.contract_address]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.public_call_requests]]
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.public_call_requests.inner]
  is_static_call = false
  calldata_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.public_call_requests.inner.msg_sender]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.public_call_requests.inner.contract_address]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.public_call_requests]]
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.public_call_requests.inner]
  is_static_call = false
  calldata_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.public_call_requests.inner.msg_sender]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.public_call_requests.inner.contract_address]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.public_call_requests]]
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.public_call_requests.inner]
  is_static_call = false
  calldata_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.public_call_requests.inner.msg_sender]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.public_call_requests.inner.contract_address]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.private_call_stack]]
args_hash = "0x2eb2d493efca5af9be1724d4c9997059e97184bf5ac7ec7d61047cb5c7f214ce"
returns_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"
start_side_effect_counter = "0x0000000000000000000000000000000000000000000000000000000000000003"
end_side_effect_counter = "0x000000000000000000000000000000000000000000000000000000000000000d"

  [previous_kernel_public_inputs.end.private_call_stack.call_context]
  is_static_call = false

    [previous_kernel_public_inputs.end.private_call_stack.call_context.msg_sender]
    inner = "0x2bd6622f715bd307d4918e462bd9aa829794c19bfe8ebbe5390237361e65e437"

    [previous_kernel_public_inputs.end.private_call_stack.call_context.contract_address]
    inner = "0x2aa83d8a54e3931f79d1923248fa2aafa441fbd0e8df1989f1fb70a910a49471"

    [previous_kernel_public_inputs.end.private_call_stack.call_context.function_selector]
    inner = "0x00000000000000000000000000000000000000000000000000000000754fb767"

[[previous_kernel_public_inputs.end.private_call_stack]]
args_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"
returns_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"
start_side_effect_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
end_side_effect_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.private_call_stack.call_context]
  is_static_call = false

    [previous_kernel_public_inputs.end.private_call_stack.call_context.msg_sender]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.private_call_stack.call_context.contract_address]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.private_call_stack.call_context.function_selector]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.private_call_stack]]
args_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"
returns_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"
start_side_effect_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
end_side_effect_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.private_call_stack.call_context]
  is_static_call = false

    [previous_kernel_public_inputs.end.private_call_stack.call_context.msg_sender]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.private_call_stack.call_context.contract_address]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.private_call_stack.call_context.function_selector]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.private_call_stack]]
args_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"
returns_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"
start_side_effect_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
end_side_effect_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.private_call_stack.call_context]
  is_static_call = false

    [previous_kernel_public_inputs.end.private_call_stack.call_context.msg_sender]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.private_call_stack.call_context.contract_address]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.private_call_stack.call_context.function_selector]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.private_call_stack]]
args_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"
returns_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"
start_side_effect_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
end_side_effect_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.private_call_stack.call_context]
  is_static_call = false

    [previous_kernel_public_inputs.end.private_call_stack.call_context.msg_sender]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.private_call_stack.call_context.contract_address]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.private_call_stack.call_context.function_selector]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.private_call_stack]]
args_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"
returns_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"
start_side_effect_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
end_side_effect_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.private_call_stack.call_context]
  is_static_call = false

    [previous_kernel_public_inputs.end.private_call_stack.call_context.msg_sender]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.private_call_stack.call_context.contract_address]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.private_call_stack.call_context.function_selector]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.private_call_stack]]
args_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"
returns_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"
start_side_effect_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
end_side_effect_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.private_call_stack.call_context]
  is_static_call = false

    [previous_kernel_public_inputs.end.private_call_stack.call_context.msg_sender]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.private_call_stack.call_context.contract_address]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.private_call_stack.call_context.function_selector]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.private_call_stack]]
args_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"
returns_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"
start_side_effect_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
end_side_effect_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.private_call_stack.call_context]
  is_static_call = false

    [previous_kernel_public_inputs.end.private_call_stack.call_context.msg_sender]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.private_call_stack.call_context.contract_address]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.private_call_stack.call_context.function_selector]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.public_teardown_call_request]
  is_static_call = false
  calldata_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.public_teardown_call_request.msg_sender]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.public_teardown_call_request.contract_address]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.fee_payer]
  inner = "0x2bd6622f715bd307d4918e462bd9aa829794c19bfe8ebbe5390237361e65e437"

[private_call.vk]
key = [
  "0x0000000000000000000000000000000000000000000000000000000000040000",
  "0x0000000000000000000000000000000000000000000000000000000000000010",
  "0x0000000000000000000000000000000000000000000000000000000000005609",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x00000000000000000000000000000000000000000000000000000000ffffffff",
  "0x00000000000000000000000000000000000000000000000000000000ffffffff",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000008c922ec8929dd2b53e48c7a1a937a0c483",
  "0x000000000000000000000000000000000005aee58715fb684e3f6222ee002865",
  "0x0000000000000000000000000000007db731afba0b515629fe7b4447a7dee68a",
  "0x00000000000000000000000000000000001512762efdadd87b386516096b95d2",
  "0x000000000000000000000000000000744dfd4d3212b5d4d68ed7b8577c895977",
  "0x000000000000000000000000000000000002a02febeeae306ad211d6d8b47744",
  "0x000000000000000000000000000000afd6a6a11aa43ecf551163a3e355688521",
  "0x00000000000000000000000000000000001dd858d5d5068e4973cfbc47dcb74e",
  "0x000000000000000000000000000000850f7e163eeae132813a2883a0671b4439",
  "0x00000000000000000000000000000000000298feebfcc80b83eff497020c9dac",
  "0x00000000000000000000000000000081bf8ca4e6ab6d03a9e46b31ecbccfd591",
  "0x00000000000000000000000000000000001e31f8dade3b3e9d44e0f8d189d26f",
  "0x0000000000000000000000000000004d6df5f67bd6de1c9e0b53cc69df4e4da6",
  "0x00000000000000000000000000000000002a3823390fbf8b6b67c56dc1adb088",
  "0x000000000000000000000000000000e665cf82926ea8fb325c06ace152124fb2",
  "0x00000000000000000000000000000000002f315eed75b093c8862cb4702731d6",
  "0x000000000000000000000000000000aeb5c7637ff790cb0370187df76a38c06d",
  "0x00000000000000000000000000000000002ac794cb5dc61537108bd5a05f0904",
  "0x000000000000000000000000000000044d85750df7612d87e54dc7dea9f07fe9",
  "0x00000000000000000000000000000000001ebab362e1d61e4bcb55f171beefb6",
  "0x00000000000000000000000000000008d6281ab0d649e56a27fdc1ea3a1d4978",
  "0x000000000000000000000000000000000019eb382253995741c3af270908f411",
  "0x0000000000000000000000000000005e28555e9862371666a3c5da5706bd084c",
  "0x00000000000000000000000000000000001ef808c39f4ce319beed6bb2da93ce",
  "0x0000000000000000000000000000000fd9156afc5984d7e2b218538e73a36ab3",
  "0x000000000000000000000000000000000011a6d5bc8a5f54ad429a8d2c202bd1",
  "0x00000000000000000000000000000072031e3e7b40ddb9a8adca76f6788398c5",
  "0x0000000000000000000000000000000000046871b27b69ac58ad6387976d0eda",
  "0x00000000000000000000000000000090d53c6a3b26339cda6b73df9208314159",
  "0x0000000000000000000000000000000000298c3311fc9170f92de940b042aab9",
  "0x000000000000000000000000000000bf37537eb196b05c1d98fa51016a9bacbb",
  "0x000000000000000000000000000000000007b05f408a612847259016b9204ae4",
  "0x0000000000000000000000000000002271dbcec3a73fb1608fa8cd17efd563c3",
  "0x000000000000000000000000000000000026f2a4ea1c1235702972ee724cb0fd",
  "0x000000000000000000000000000000102ee57c755bedeb4fc5813937b79a8b49",
  "0x0000000000000000000000000000000000112c26413abcadf1b6ff5e355bfc9a",
  "0x0000000000000000000000000000008a5a47cbe36a1d779d4674e5235e975d83",
  "0x00000000000000000000000000000000001b4d8b0f9a9413d3eb578af4c1142d",
  "0x00000000000000000000000000000075d8d775bcf1c6854a2b8f1da74ecdae1f",
  "0x00000000000000000000000000000000001fbf140bc966e4bc360213e733b857",
  "0x0000000000000000000000000000004f3a45ad95852cf40fb9131f70ba613b8e",
  "0x0000000000000000000000000000000000051e9da46dea9786f157819588b840",
  "0x0000000000000000000000000000005aefb5fb3d7125df85dd1fa6dbd523499f",
  "0x00000000000000000000000000000000000df33ba612a18ede34cc78f1a901a3",
  "0x000000000000000000000000000000df6a5f52008639efb2dad637f79cc53c46",
  "0x00000000000000000000000000000000002336ebc2da189a420934850c9d524e",
  "0x00000000000000000000000000000060f73612cb2fff6ff960665d1e338f66c9",
  "0x00000000000000000000000000000000002a6e53bcb9bac59ade2bd467d51f0c",
  "0x000000000000000000000000000000a2678f977ced1819f59c26d58c09da3f81",
  "0x00000000000000000000000000000000000bbe20599d56769424cf2fd08947c8",
  "0x0000000000000000000000000000004a53704858983d2b35bfe6d225e802e18c",
  "0x00000000000000000000000000000000002eecc4709b1f98a0559a6769402022",
  "0x000000000000000000000000000000960eac2503465e7864f3f7ecc684911eb3",
  "0x000000000000000000000000000000000001463ecae36438320b360bd71b7888",
  "0x00000000000000000000000000000088257e741df14f015b89d28352cec7d0a7",
  "0x0000000000000000000000000000000000079c8ab18ac2978c5c3cab4f4840e2",
  "0x000000000000000000000000000000603194bcea2524b50c93a295c36d66c833",
  "0x000000000000000000000000000000000004bd69fc60b1e8fdafd60c6416d479",
  "0x000000000000000000000000000000798be233344a5e7b2d5cf5a0bf9f7694b9",
  "0x00000000000000000000000000000000002da58cffeeef522c7217146c33d7fd",
  "0x000000000000000000000000000000d6aa29930ed32786d5d638c0b9653acb55",
  "0x000000000000000000000000000000000000f4e582efb957e971f4031aaf938b",
  "0x00000000000000000000000000000076529b8f4c18e87d0ea45b38be1450e042",
  "0x00000000000000000000000000000000002761ced8e2cacc403045b51d6b0a82",
  "0x00000000000000000000000000000029885a116e4400f644081582005be51a85",
  "0x0000000000000000000000000000000000180f6c9c00e8210effd96aba660bdd",
  "0x000000000000000000000000000000cc4483ccaffb76ba2772aa0590b95c3123",
  "0x0000000000000000000000000000000000197411412232a226f5f26b869e7559",
  "0x00000000000000000000000000000078f511ace8ccc4599ef2316c78294bbde8",
  "0x000000000000000000000000000000000002de445a3d8bf106c635ba5c819b5d",
  "0x0000000000000000000000000000004957794ac5c253ee10b67133b380e8b449",
  "0x0000000000000000000000000000000000202dc5472fc3e6ced5309d7656b8ef",
  "0x000000000000000000000000000000eeee2f16bdb9ee4265bbb8541f2e6cadf8",
  "0x000000000000000000000000000000000008543427edff0f19ba66fd23648224",
  "0x000000000000000000000000000000cf4be72d13803e905ccc8c86b8b579e42e",
  "0x000000000000000000000000000000000009441dc88ecd7086ec8d301c2b992b",
  "0x000000000000000000000000000000b3358676733524e06292022c2d628a9af7",
  "0x00000000000000000000000000000000001f87eb1bae21916153eb74a8696d6b",
  "0x000000000000000000000000000000f3e695d10bb18af982e2869a0533ab07a9",
  "0x00000000000000000000000000000000002b4fc3cdeaa0f5fe6e58a56c67e112",
  "0x000000000000000000000000000000a13aec16952c70a1c0ee913fb2c4783323",
  "0x00000000000000000000000000000000001c369e552dc209b0cdd9dfd42cf34e",
  "0x000000000000000000000000000000977f46400637c0fe3dc2bd6b80efbacf68",
  "0x00000000000000000000000000000000001a67b3e4d5d05e2860425548e20bda",
  "0x000000000000000000000000000000b828dd508f52d269280bfd84cfe39a779e",
  "0x0000000000000000000000000000000000172ed3faa93b618002271c5be01f5a",
  "0x0000000000000000000000000000006a8a994346ee901a78fa3d3616f943d39b",
  "0x000000000000000000000000000000000027034787d0180a5851353a3fea8558",
  "0x000000000000000000000000000000f6f4596202301b6ae4eb0ebbeadd203340",
  "0x00000000000000000000000000000000000adc89c48d75b571636f5bbeb4a806",
  "0x00000000000000000000000000000000034e3e27454ef992b4bf84b97baa7471",
  "0x0000000000000000000000000000000000066f28135748f119631c3fe07fa9d7",
  "0x0000000000000000000000000000003b64a66f2ac4979b65e56568c5a31b14ed",
  "0x00000000000000000000000000000000002e25783551df50c004ec7cd1f4dd8b",
  "0x000000000000000000000000000000e8258f84477c1b62565a559ba7bb38832e",
  "0x000000000000000000000000000000000018f76cf0ceeccb4798de741ae89b64",
  "0x000000000000000000000000000000353d43fa70e99239c1c1c67e271a0eeac5",
  "0x00000000000000000000000000000000002d299fb68678d0150bcc5b16dc8252",
  "0x0000000000000000000000000000002814ede7cd27daed00c33c12860bc4b046",
  "0x000000000000000000000000000000000015d3ac5a199abb74933a4efc98c59b",
  "0x000000000000000000000000000000469680c270e551515344592f59188fa765",
  "0x00000000000000000000000000000000002d38d6d4ba1e4763a74ecdb11ca1f3",
  "0x000000000000000000000000000000fce917c0d5dca019477c52f6075332b612",
  "0x000000000000000000000000000000000012db39e892826b32610ee08251e005",
  "0x0000000000000000000000000000000000000000000000000000000000000001",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000002",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x000000000000000000000000000000379cd048520ef223052dec68a876f87c5e",
  "0x00000000000000000000000000000000002b8b2bd10bed6d6d95f972a356903a",
  "0x000000000000000000000000000000a56d6840de8411ec0a52e26e6dc407eb03",
  "0x00000000000000000000000000000000002703f96e5b8434afa2fc404e423d32",
  "0x0000000000000000000000000000006bcc7a05ff95a96b289424c5f733670d96",
  "0x000000000000000000000000000000000000c43726f75b6fda0de22ce0e0dfab",
  "0x0000000000000000000000000000001d0a09d7178ec93bad7858f96e64f0b48d",
  "0x00000000000000000000000000000000002f9b6e0b4e2c01968de5c32482aa7d",
  "0x000000000000000000000000000000e55ba19751adfe6c36324d3fb6c2da0989",
  "0x00000000000000000000000000000000001d58aa61c64ad522043d79c4802219",
  "0x00000000000000000000000000000078f4b3bc61f19d6e7069359bbf47e7f907",
  "0x00000000000000000000000000000000002d7c18a93c3dae58809faaeec6a86a"
]
hash = "0x040c81af0aaf12be481cb44cda9050d54baab80ae3391bd62c45ed3ccc4c6181"

[private_call.verification_key_hints]
contract_class_artifact_hash = "0x1eb011833963f3fa143e49e98c21682967a28ac3aa307167ffb573afd66dddd7"
contract_class_public_bytecode_commitment = "0x22a335259f39a0100c437728d1a791f9e243a7141a8aba3861b86a227c5a5656"
updated_class_id_shared_mutable_values = [
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000"
]

  [private_call.verification_key_hints.function_leaf_membership_witness]
  leaf_index = "4"
  sibling_path = [
  "0x00c9e06f22a9b077dd060eb911df0927fff93c5566c11594e4b40fb4f3c8b406",
  "0x1eb25301b6f5bed89949a2550124ae3a9535daea7ee79fb81358ca5c9a46a231",
  "0x0906ed5ebe89a5f28ef6b82d508e091accf3c27cfa1950ce64bbec8e49f5ef43",
  "0x0fb3f114e2f4ac76c777b673950a9a2777b151393380577c60a7247f825ae4d2",
  "0x2a6595890719fef7967c209488728aa5342438ba52058a3c770202f55acf6854"
]

[private_call.verification_key_hints.public_keys.npk_m.inner]
x = "0x01498945581e0eb9f8427ad6021184c700ef091d570892c437d12c7d90364bbd"
y = "0x170ae506787c5c43d6ca9255d571c10fa9ffa9d141666e290c347c5c9ab7e344"
is_infinite = false

[private_call.verification_key_hints.public_keys.ivpk_m.inner]
x = "0x00c044b05b6ca83b9c2dbae79cc1135155956a64e136819136e9947fe5e5866c"
y = "0x1c1f0ca244c7cd46b682552bff8ae77dea40b966a71de076ec3b7678f2bdb151"
is_infinite = false

[private_call.verification_key_hints.public_keys.ovpk_m.inner]
x = "0x1b00316144359e9a3ec8e49c1cdb7eeb0cedd190dfd9dc90eea5115aa779e287"
y = "0x080ffc74d7a8b0bccb88ac11f45874172f3847eb8b92654aaa58a3d2b8dc7833"
is_infinite = false

[private_call.verification_key_hints.public_keys.tpk_m.inner]
x = "0x019c111f36ad3fc1d9b7a7a14344314d2864b94f030594cd67f753ef774a1efb"
y = "0x2039907fe37f08d10739255141bb066c506a12f7d1e8dfec21abc58494705b6f"
is_infinite = false

  [private_call.verification_key_hints.salted_initialization_hash]
  inner = "0x1be6c4dd17ec4dff71379c8266b92926ee458a76d9bbba3e65fd00f52a516ce0"

  [private_call.verification_key_hints.protocol_contract_membership_witness]
  leaf_index = "5"
  sibling_path = [
  "0x2bcd6da5c9cf56c2dbac35dcbfc20a8fd15051f81577c32f489e5e81c6eecfe8",
  "0x1e4dfdec09cf3cf09e50a2b5dd79fc3625f4f26744a15437b7df2f8e718b6d8f",
  "0x1ae7aba6971a2bb72fcf70e1376077b8f3c30441a8fabf4ddb29c599e539d4b7"
]

  [private_call.verification_key_hints.protocol_contract_leaf]
  address = "0x212209c22c967eecc546657e4fdcfdb0652dcff93b6a3d906b26cc1664c3829b"
  next_address = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [private_call.verification_key_hints.updated_class_id_witness]
  leaf_index = "130"
  sibling_path = [
  "0x09958a63a4504bc5186bb11355ccbe93822bd33e2349c9848d619683f4878e35",
  "0x2a303b047b4584523aafcb8439a4dcd20422f8a57dc671634c02d987e2cbe181",
  "0x0ad75e108463094e527f888cfdd2cea13675393125d53b907cb65c981f5e8a4f",
  "0x1f608a22cf0921a7daca65a1ba55eb0e369f47d77d66d21c14fa7ade8211ef3e",
  "0x2373ea368857ec7af97e7b470d705848e2bf93ed7bef142a490f2119bcf82d8e",
  "0x120157cfaaa49ce3da30f8b47879114977c24b266d58b0ac18b325d878aafddf",
  "0x01c28fe1059ae0237b72334700697bdf465e03df03986fe05200cadeda66bd76",
  "0x2dbb51a7e0e06fe0ff6417d3052d11735ba22d33afd7424512bcb079915543bb",
  "0x067243231eddf4222f3911defbba7705aff06ed45960b27f6f91319196ef97e1",
  "0x1849b85f3c693693e732dfc4577217acc18295193bede09ce8b97ad910310972",
  "0x2a775ea761d20435b31fa2c33ff07663e24542ffb9e7b293dfce3042eb104686",
  "0x0f320b0703439a8114f81593de99cd0b8f3b9bf854601abb5b2ea0e8a3dda4a7",
  "0x0d07f6e7a8a0e9199d6d92801fff867002ff5b4808962f9da2ba5ce1bdd26a73",
  "0x1c4954081e324939350febc2b918a293ebcdaead01be95ec02fcbe8d2c1635d1",
  "0x0197f2171ef99c2d053ee1fb5ff5ab288d56b9b41b4716c9214a4d97facc4c4a",
  "0x2b9cdd484c5ba1e4d6efcc3f18734b5ac4c4a0b9102e2aeb48521a661d3feee9",
  "0x14f44d672eb357739e42463497f9fdac46623af863eea4d947ca00a497dcdeb3",
  "0x071d7627ae3b2eabda8a810227bf04206370ac78dbf6c372380182dbd3711fe3",
  "0x2fdc08d9fe075ac58cb8c00f98697861a13b3ab6f9d41a4e768f75e477475bf5",
  "0x20165fe405652104dceaeeca92950aa5adc571b8cafe192878cba58ff1be49c5",
  "0x1c8c3ca0b3a3d75850fcd4dc7bf1e3445cd0cfff3ca510630fd90b47e8a24755",
  "0x1f0c1a8fb16b0d2ac9a146d7ae20d8d179695a92a79ed66fc45d9da4532459b3",
  "0x038146ec5a2573e1c30d2fb32c66c8440f426fbd108082df41c7bebd1d521c30",
  "0x17d3d12b17fe762de4b835b2180b012e808816a7f2ff69ecb9d65188235d8fd4",
  "0x0e1a6b7d63a6e5a9e54e8f391dd4e9d49cdfedcbc87f02cd34d4641d2eb30491",
  "0x09244eec34977ff795fc41036996ce974136377f521ac8eb9e04642d204783d2",
  "0x1646d6f544ec36df9dc41f778a7ef1690a53c730b501471b6acd202194a7e8e9",
  "0x064769603ba3f6c41f664d266ecb9a3a0f6567cd3e48b40f34d4894ee4c361b3",
  "0x1595bb3cd19f84619dc2e368175a88d8627a7439eda9397202cdb1167531fd3f",
  "0x2a529be462b81ca30265b558763b1498289c9d88277ab14f0838cb1fce4b472c",
  "0x0c08da612363088ad0bbc78abd233e8ace4c05a56fdabdd5e5e9b05e428bdaee",
  "0x14748d0241710ef47f54b931ac5a58082b1d56b0f0c30d55fb71a6e8c9a6be14",
  "0x0b59baa35b9dc267744f0ccb4e3b0255c1fc512460d91130c6bc19fb2668568d",
  "0x2c45bb0c3d5bc1dc98e0baef09ff46d18c1a451e724f41c2b675549bb5c80e59",
  "0x121468e6710bf1ffec6d0f26743afe6f88ef55dab40b83ca0a39bc44b196374c",
  "0x2042c32c823a7440ceb6c342f9125f1fe426b02c527cd8fb28c85d02b705e759",
  "0x0d582c10ff8115413aa5b70564fdd2f3cefe1f33a1e43a47bc495081e91e73e5",
  "0x0f55a0d491a9da093eb999fa0dffaf904620cbc78d07e63c6f795c5c7512b523",
  "0x21849764e1aa64b83a69e39d27eedaec2a8f97066e5ddb74634ffdb11388dd9a",
  "0x2e33ee2008411c04b99c24b313513d097a0d21a5040b6193d1f978b8226892d6"
]

  [private_call.verification_key_hints.updated_class_id_leaf]
  slot = "0x057cee37f4bd8d9d220091baa0fc80fb265cc559cd855cb4d5679c01752192bc"
  value = "0x0055534443000000000000000000000000000000000000000000000000000000"
  next_slot = "0x0a9638c12d58973aac2b4b150bd39b9380d44eef177269dbf0ea0adaf37c76aa"
  next_index = "0x0000000000000000000000000000000000000000000000000000000000000084"

[app_public_inputs]
args_hash = "0x2eb2d493efca5af9be1724d4c9997059e97184bf5ac7ec7d61047cb5c7f214ce"
returns_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"
start_side_effect_counter = "0x0000000000000000000000000000000000000000000000000000000000000003"
end_side_effect_counter = "0x000000000000000000000000000000000000000000000000000000000000000d"
min_revertible_side_effect_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
is_fee_payer = false

[app_public_inputs.max_block_number._opt]
_is_some = false
_value = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [app_public_inputs.call_context]
  is_static_call = false

    [app_public_inputs.call_context.msg_sender]
    inner = "0x2bd6622f715bd307d4918e462bd9aa829794c19bfe8ebbe5390237361e65e437"

    [app_public_inputs.call_context.contract_address]
    inner = "0x2aa83d8a54e3931f79d1923248fa2aafa441fbd0e8df1989f1fb70a910a49471"

    [app_public_inputs.call_context.function_selector]
    inner = "0x00000000000000000000000000000000000000000000000000000000754fb767"

  [[app_public_inputs.note_hash_read_requests]]
  value = "0x242fdb074c60f73a5d343deae3c95f531fc463bd45f09c3e75f91c1ccdfe6f1a"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000005"

  [[app_public_inputs.note_hash_read_requests]]
  value = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.note_hash_read_requests]]
  value = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.note_hash_read_requests]]
  value = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.note_hash_read_requests]]
  value = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.note_hash_read_requests]]
  value = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.note_hash_read_requests]]
  value = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.note_hash_read_requests]]
  value = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.note_hash_read_requests]]
  value = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.note_hash_read_requests]]
  value = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.note_hash_read_requests]]
  value = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.note_hash_read_requests]]
  value = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.note_hash_read_requests]]
  value = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.note_hash_read_requests]]
  value = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.note_hash_read_requests]]
  value = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.note_hash_read_requests]]
  value = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.nullifier_read_requests]]
  value = "0x2aa83d8a54e3931f79d1923248fa2aafa441fbd0e8df1989f1fb70a910a49471"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000004"

  [[app_public_inputs.nullifier_read_requests]]
  value = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.nullifier_read_requests]]
  value = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.nullifier_read_requests]]
  value = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.nullifier_read_requests]]
  value = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.nullifier_read_requests]]
  value = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.nullifier_read_requests]]
  value = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.nullifier_read_requests]]
  value = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.nullifier_read_requests]]
  value = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.nullifier_read_requests]]
  value = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.nullifier_read_requests]]
  value = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.nullifier_read_requests]]
  value = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.nullifier_read_requests]]
  value = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.nullifier_read_requests]]
  value = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.nullifier_read_requests]]
  value = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.nullifier_read_requests]]
  value = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.key_validation_requests_and_generators]]
  sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000030"

    [app_public_inputs.key_validation_requests_and_generators.request]
    sk_app = "0x20d0d65fb94616f3fb2a12aba4a4d0c7895201a706a392a7f175d26d0b16390f"

      [app_public_inputs.key_validation_requests_and_generators.request.pk_m]
      x = "0x28dd2b7fddec890b822ca683d378afc9a5c40082602bf935c0a996d5a477043b"
      y = "0x23ff8c1cb51f4e90e29b7650d95714626eca1f7880692b116891fae901e42217"
      is_infinite = false

  [[app_public_inputs.key_validation_requests_and_generators]]
  sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [app_public_inputs.key_validation_requests_and_generators.request]
    sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

      [app_public_inputs.key_validation_requests_and_generators.request.pk_m]
      x = "0x0000000000000000000000000000000000000000000000000000000000000000"
      y = "0x0000000000000000000000000000000000000000000000000000000000000000"
      is_infinite = false

  [[app_public_inputs.key_validation_requests_and_generators]]
  sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [app_public_inputs.key_validation_requests_and_generators.request]
    sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

      [app_public_inputs.key_validation_requests_and_generators.request.pk_m]
      x = "0x0000000000000000000000000000000000000000000000000000000000000000"
      y = "0x0000000000000000000000000000000000000000000000000000000000000000"
      is_infinite = false

  [[app_public_inputs.key_validation_requests_and_generators]]
  sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [app_public_inputs.key_validation_requests_and_generators.request]
    sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

      [app_public_inputs.key_validation_requests_and_generators.request.pk_m]
      x = "0x0000000000000000000000000000000000000000000000000000000000000000"
      y = "0x0000000000000000000000000000000000000000000000000000000000000000"
      is_infinite = false

  [[app_public_inputs.key_validation_requests_and_generators]]
  sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [app_public_inputs.key_validation_requests_and_generators.request]
    sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

      [app_public_inputs.key_validation_requests_and_generators.request.pk_m]
      x = "0x0000000000000000000000000000000000000000000000000000000000000000"
      y = "0x0000000000000000000000000000000000000000000000000000000000000000"
      is_infinite = false

  [[app_public_inputs.key_validation_requests_and_generators]]
  sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [app_public_inputs.key_validation_requests_and_generators.request]
    sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

      [app_public_inputs.key_validation_requests_and_generators.request.pk_m]
      x = "0x0000000000000000000000000000000000000000000000000000000000000000"
      y = "0x0000000000000000000000000000000000000000000000000000000000000000"
      is_infinite = false

  [[app_public_inputs.key_validation_requests_and_generators]]
  sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [app_public_inputs.key_validation_requests_and_generators.request]
    sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

      [app_public_inputs.key_validation_requests_and_generators.request.pk_m]
      x = "0x0000000000000000000000000000000000000000000000000000000000000000"
      y = "0x0000000000000000000000000000000000000000000000000000000000000000"
      is_infinite = false

  [[app_public_inputs.key_validation_requests_and_generators]]
  sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [app_public_inputs.key_validation_requests_and_generators.request]
    sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

      [app_public_inputs.key_validation_requests_and_generators.request.pk_m]
      x = "0x0000000000000000000000000000000000000000000000000000000000000000"
      y = "0x0000000000000000000000000000000000000000000000000000000000000000"
      is_infinite = false

  [[app_public_inputs.key_validation_requests_and_generators]]
  sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [app_public_inputs.key_validation_requests_and_generators.request]
    sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

      [app_public_inputs.key_validation_requests_and_generators.request.pk_m]
      x = "0x0000000000000000000000000000000000000000000000000000000000000000"
      y = "0x0000000000000000000000000000000000000000000000000000000000000000"
      is_infinite = false

  [[app_public_inputs.key_validation_requests_and_generators]]
  sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [app_public_inputs.key_validation_requests_and_generators.request]
    sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

      [app_public_inputs.key_validation_requests_and_generators.request.pk_m]
      x = "0x0000000000000000000000000000000000000000000000000000000000000000"
      y = "0x0000000000000000000000000000000000000000000000000000000000000000"
      is_infinite = false

  [[app_public_inputs.key_validation_requests_and_generators]]
  sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [app_public_inputs.key_validation_requests_and_generators.request]
    sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

      [app_public_inputs.key_validation_requests_and_generators.request.pk_m]
      x = "0x0000000000000000000000000000000000000000000000000000000000000000"
      y = "0x0000000000000000000000000000000000000000000000000000000000000000"
      is_infinite = false

  [[app_public_inputs.key_validation_requests_and_generators]]
  sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [app_public_inputs.key_validation_requests_and_generators.request]
    sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

      [app_public_inputs.key_validation_requests_and_generators.request.pk_m]
      x = "0x0000000000000000000000000000000000000000000000000000000000000000"
      y = "0x0000000000000000000000000000000000000000000000000000000000000000"
      is_infinite = false

  [[app_public_inputs.key_validation_requests_and_generators]]
  sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [app_public_inputs.key_validation_requests_and_generators.request]
    sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

      [app_public_inputs.key_validation_requests_and_generators.request.pk_m]
      x = "0x0000000000000000000000000000000000000000000000000000000000000000"
      y = "0x0000000000000000000000000000000000000000000000000000000000000000"
      is_infinite = false

  [[app_public_inputs.key_validation_requests_and_generators]]
  sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [app_public_inputs.key_validation_requests_and_generators.request]
    sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

      [app_public_inputs.key_validation_requests_and_generators.request.pk_m]
      x = "0x0000000000000000000000000000000000000000000000000000000000000000"
      y = "0x0000000000000000000000000000000000000000000000000000000000000000"
      is_infinite = false

  [[app_public_inputs.key_validation_requests_and_generators]]
  sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [app_public_inputs.key_validation_requests_and_generators.request]
    sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

      [app_public_inputs.key_validation_requests_and_generators.request.pk_m]
      x = "0x0000000000000000000000000000000000000000000000000000000000000000"
      y = "0x0000000000000000000000000000000000000000000000000000000000000000"
      is_infinite = false

  [[app_public_inputs.key_validation_requests_and_generators]]
  sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [app_public_inputs.key_validation_requests_and_generators.request]
    sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

      [app_public_inputs.key_validation_requests_and_generators.request.pk_m]
      x = "0x0000000000000000000000000000000000000000000000000000000000000000"
      y = "0x0000000000000000000000000000000000000000000000000000000000000000"
      is_infinite = false

  [[app_public_inputs.note_hashes]]
  value = "0x196b4b63eeb37620b8bd8976b61d565ca2c0bfa95f32065a268061799a6078ab"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000007"

  [[app_public_inputs.note_hashes]]
  value = "0x2af8e4a2ac2e195be1d0fbf54be1df6cf6518ead0d936ceb5005837fd03d2775"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000009"

  [[app_public_inputs.note_hashes]]
  value = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.note_hashes]]
  value = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.note_hashes]]
  value = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.note_hashes]]
  value = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.note_hashes]]
  value = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.note_hashes]]
  value = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.note_hashes]]
  value = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.note_hashes]]
  value = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.note_hashes]]
  value = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.note_hashes]]
  value = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.note_hashes]]
  value = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.note_hashes]]
  value = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.note_hashes]]
  value = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.note_hashes]]
  value = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.nullifiers]]
  value = "0x09a9ae6ccf7c8ffbe9c4b34a87d5cc8ad0ebfcfbd30d1cb03b931914f6f71526"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000006"
  note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.nullifiers]]
  value = "0x035ef370751cd44ab408b168ff612e15ff09ecdbb6e17cdf053d537e40fdc657"
  counter = "0x000000000000000000000000000000000000000000000000000000000000000b"
  note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.nullifiers]]
  value = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
  note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.nullifiers]]
  value = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
  note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.nullifiers]]
  value = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
  note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.nullifiers]]
  value = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
  note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.nullifiers]]
  value = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
  note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.nullifiers]]
  value = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
  note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.nullifiers]]
  value = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
  note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.nullifiers]]
  value = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
  note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.nullifiers]]
  value = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
  note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.nullifiers]]
  value = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
  note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.nullifiers]]
  value = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
  note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.nullifiers]]
  value = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
  note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.nullifiers]]
  value = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
  note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.nullifiers]]
  value = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
  note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.private_call_requests]]
  args_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"
  returns_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"
  start_side_effect_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
  end_side_effect_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [app_public_inputs.private_call_requests.call_context]
    is_static_call = false

      [app_public_inputs.private_call_requests.call_context.msg_sender]
      inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

      [app_public_inputs.private_call_requests.call_context.contract_address]
      inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

      [app_public_inputs.private_call_requests.call_context.function_selector]
      inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.private_call_requests]]
  args_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"
  returns_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"
  start_side_effect_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
  end_side_effect_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [app_public_inputs.private_call_requests.call_context]
    is_static_call = false

      [app_public_inputs.private_call_requests.call_context.msg_sender]
      inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

      [app_public_inputs.private_call_requests.call_context.contract_address]
      inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

      [app_public_inputs.private_call_requests.call_context.function_selector]
      inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.private_call_requests]]
  args_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"
  returns_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"
  start_side_effect_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
  end_side_effect_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [app_public_inputs.private_call_requests.call_context]
    is_static_call = false

      [app_public_inputs.private_call_requests.call_context.msg_sender]
      inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

      [app_public_inputs.private_call_requests.call_context.contract_address]
      inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

      [app_public_inputs.private_call_requests.call_context.function_selector]
      inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.private_call_requests]]
  args_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"
  returns_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"
  start_side_effect_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
  end_side_effect_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [app_public_inputs.private_call_requests.call_context]
    is_static_call = false

      [app_public_inputs.private_call_requests.call_context.msg_sender]
      inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

      [app_public_inputs.private_call_requests.call_context.contract_address]
      inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

      [app_public_inputs.private_call_requests.call_context.function_selector]
      inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.private_call_requests]]
  args_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"
  returns_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"
  start_side_effect_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
  end_side_effect_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [app_public_inputs.private_call_requests.call_context]
    is_static_call = false

      [app_public_inputs.private_call_requests.call_context.msg_sender]
      inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

      [app_public_inputs.private_call_requests.call_context.contract_address]
      inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

      [app_public_inputs.private_call_requests.call_context.function_selector]
      inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.public_call_requests]]
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [app_public_inputs.public_call_requests.inner]
    is_static_call = false
    calldata_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

      [app_public_inputs.public_call_requests.inner.msg_sender]
      inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

      [app_public_inputs.public_call_requests.inner.contract_address]
      inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.public_call_requests]]
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [app_public_inputs.public_call_requests.inner]
    is_static_call = false
    calldata_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

      [app_public_inputs.public_call_requests.inner.msg_sender]
      inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

      [app_public_inputs.public_call_requests.inner.contract_address]
      inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.public_call_requests]]
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [app_public_inputs.public_call_requests.inner]
    is_static_call = false
    calldata_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

      [app_public_inputs.public_call_requests.inner.msg_sender]
      inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

      [app_public_inputs.public_call_requests.inner.contract_address]
      inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.public_call_requests]]
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [app_public_inputs.public_call_requests.inner]
    is_static_call = false
    calldata_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

      [app_public_inputs.public_call_requests.inner.msg_sender]
      inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

      [app_public_inputs.public_call_requests.inner.contract_address]
      inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.public_call_requests]]
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [app_public_inputs.public_call_requests.inner]
    is_static_call = false
    calldata_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

      [app_public_inputs.public_call_requests.inner.msg_sender]
      inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

      [app_public_inputs.public_call_requests.inner.contract_address]
      inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.public_call_requests]]
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [app_public_inputs.public_call_requests.inner]
    is_static_call = false
    calldata_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

      [app_public_inputs.public_call_requests.inner.msg_sender]
      inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

      [app_public_inputs.public_call_requests.inner.contract_address]
      inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.public_call_requests]]
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [app_public_inputs.public_call_requests.inner]
    is_static_call = false
    calldata_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

      [app_public_inputs.public_call_requests.inner.msg_sender]
      inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

      [app_public_inputs.public_call_requests.inner.contract_address]
      inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.public_call_requests]]
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [app_public_inputs.public_call_requests.inner]
    is_static_call = false
    calldata_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

      [app_public_inputs.public_call_requests.inner.msg_sender]
      inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

      [app_public_inputs.public_call_requests.inner.contract_address]
      inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.public_call_requests]]
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [app_public_inputs.public_call_requests.inner]
    is_static_call = false
    calldata_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

      [app_public_inputs.public_call_requests.inner.msg_sender]
      inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

      [app_public_inputs.public_call_requests.inner.contract_address]
      inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.public_call_requests]]
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [app_public_inputs.public_call_requests.inner]
    is_static_call = false
    calldata_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

      [app_public_inputs.public_call_requests.inner.msg_sender]
      inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

      [app_public_inputs.public_call_requests.inner.contract_address]
      inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.public_call_requests]]
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [app_public_inputs.public_call_requests.inner]
    is_static_call = false
    calldata_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

      [app_public_inputs.public_call_requests.inner.msg_sender]
      inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

      [app_public_inputs.public_call_requests.inner.contract_address]
      inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.public_call_requests]]
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [app_public_inputs.public_call_requests.inner]
    is_static_call = false
    calldata_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

      [app_public_inputs.public_call_requests.inner.msg_sender]
      inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

      [app_public_inputs.public_call_requests.inner.contract_address]
      inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.public_call_requests]]
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [app_public_inputs.public_call_requests.inner]
    is_static_call = false
    calldata_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

      [app_public_inputs.public_call_requests.inner.msg_sender]
      inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

      [app_public_inputs.public_call_requests.inner.contract_address]
      inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.public_call_requests]]
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [app_public_inputs.public_call_requests.inner]
    is_static_call = false
    calldata_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

      [app_public_inputs.public_call_requests.inner.msg_sender]
      inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

      [app_public_inputs.public_call_requests.inner.contract_address]
      inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.public_call_requests]]
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [app_public_inputs.public_call_requests.inner]
    is_static_call = false
    calldata_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

      [app_public_inputs.public_call_requests.inner.msg_sender]
      inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

      [app_public_inputs.public_call_requests.inner.contract_address]
      inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.public_call_requests]]
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [app_public_inputs.public_call_requests.inner]
    is_static_call = false
    calldata_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

      [app_public_inputs.public_call_requests.inner.msg_sender]
      inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

      [app_public_inputs.public_call_requests.inner.contract_address]
      inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [app_public_inputs.public_teardown_call_request]
  is_static_call = false
  calldata_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [app_public_inputs.public_teardown_call_request.msg_sender]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [app_public_inputs.public_teardown_call_request.contract_address]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.l2_to_l1_msgs]]
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [app_public_inputs.l2_to_l1_msgs.inner]
    content = "0x0000000000000000000000000000000000000000000000000000000000000000"

      [app_public_inputs.l2_to_l1_msgs.inner.recipient]
      inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.l2_to_l1_msgs]]
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [app_public_inputs.l2_to_l1_msgs.inner]
    content = "0x0000000000000000000000000000000000000000000000000000000000000000"

      [app_public_inputs.l2_to_l1_msgs.inner.recipient]
      inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.private_logs]]
  note_hash_counter = "0x0000000000000000000000000000000000000000000000000000000000000007"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000008"

    [app_public_inputs.private_logs.log]
    fields = [
  "0x17f017bd630fae0eec8135edbff333f6d89fc8940a8887093277555a602ad7c3",
  "0x1466b178c62c97c25d8a089cbdc3194655a371a0af046c0c7284bd68441a042d",
  "0x000183bcb2905d56c7c48222ce689f860d220ab6efdfe56c50be00043683781e",
  "0x00f037e12d8e092b12ba48f73d2ea9806dcd83413d330ef8dcb648b264d000c3",
  "0x00e51f7c22ffc356c5fd94d7ee48e07d42eff5f951ea7e3c581b2f9f75b926cb",
  "0x00202d06923694891aee33288b3c63259ef7aa77741a0562b1b127c4ce3ee5a8",
  "0x0000ed40316e807dafd299533477398cb726de8f5adc48af573cad6d5a62006a",
  "0x00ba120b174daba4e931c0fc8c3fe814392b6fb223374096098217932d5a7a3f",
  "0x00ea54809e230e188210b8e776a10ea16fc57e1126d5c5dc362547a6d07cdad5",
  "0x00c50d7ae9b59241ec81574d6b336c330e4f2a7e44ae8fffdf53155413a1f14c",
  "0x00af8fba3a4058f4903cad2c9d984cdeb6478db9276117f96e8e301007096714",
  "0x00a10aa0c1d79c568a58406b89ae50ae9b8f85e2fa930b18a0013973938672b5",
  "0x0064abeebe0923de3c138abd4255b7652f4477274a734510f1a2e1a13e136fdd",
  "0x00bc36d20e89e71862820292710c3e185ce9eb1b44ff71d1c640a6b4739325f4",
  "0x0061121a4f78fc12b2612757096d634bc91f76b5f79106e0b5f95538f1a1d8ee",
  "0x0063cf5da733ac034941cd9f27d57c8d09d7ca41c53df4874c203ae73a5e2f10",
  "0x0080840e59dec11fdcd00224cc284f94cd28456b22f0348bfa482671a0aeef7f",
  "0x006e21537dbd0d683a797a60f677558959fbf132d5e500fef2b21db1ba80746c"
]
    length = "0x0000000000000000000000000000000000000000000000000000000000000012"

  [[app_public_inputs.private_logs]]
  note_hash_counter = "0x0000000000000000000000000000000000000000000000000000000000000009"
  counter = "0x000000000000000000000000000000000000000000000000000000000000000a"

    [app_public_inputs.private_logs.log]
    fields = [
  "0x1e8854462554f860366080c97fbc34ca7bf54b3e44f8866d8c21671568a7df18",
  "0x2d28194e0425c2aa2448f64ef4cc4cf433688fa902e4aff82a46a8eee0fb63e3",
  "0x00000ba9e306913bed3b63745ca98d1fb0c98b816a3f6113ac4862b87463bf99",
  "0x00ecda7cd91d63a809a4fdcff4cca394883f564e5710e67cce038fd861eba6b2",
  "0x004883aabf031466b62befce4c7920e1588812118e4b5ba5f63059a253321c94",
  "0x005d254a555d4b2ec59f3fe5442bfdb2a6127a0cf4beec0d2494e7c901dbdb9a",
  "0x00b49eff48060025f0946df185f2d94c61cee564db481b023d92661bdffec2d4",
  "0x00bc937447ee3a6b831e69c8235fcc58ec1d59ae37ae13578d85e76f50a30e7f",
  "0x00af362f331758e0ccb42f286063c7ded428325b2ade06e8c0c45d50854f8bda",
  "0x0031b4a059aa63cbae91402b211e2aa77545ef65ecc661a782ddb2eef8ac2d33",
  "0x00e63e29865ca673787fd320ef260ccea18e5d50f17a2d8b00a1cc74bbeb310a",
  "0x00d8e8d88d89a6ef5831c886c2b86bd31dccdd1c44bd2bf0d8d3a42f283c054c",
  "0x00ad485aca04ecf3577b6c2864f3706aae3b6c99fac8825fe77df6e9a8982bb3",
  "0x00988c28004e271b4319e176d52e54c503f0f003ad9b9bdd412c5591c10ac98d",
  "0x00708c935a9915fe97925867d01a996a0a0a6bd74dc839719818235495570fc2",
  "0x00da7a9e39b3fbe606e695f1e7d992ac803a97225d48b870150fafd285660776",
  "0x00ec137b4da68cc2777df0bb2819fd326678e2882a10f042ba102abb6633df56",
  "0x00871a86bab19272fcc1ceed5cf6d3744b10e999fa593c0415fd7337fd43b80c"
]
    length = "0x0000000000000000000000000000000000000000000000000000000000000012"

  [[app_public_inputs.private_logs]]
  note_hash_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x000000000000000000000000000000000000000000000000000000000000000c"

    [app_public_inputs.private_logs.log]
    fields = [
  "0x2fe217f09c3d8eac849e654013828c496deb2a6f0f11b0da1dc8fe4b7f8d41d0",
  "0x0e7e793538507c5dd47fd657a0729391fed5933d4daaaba6aca272c26b6875ce",
  "0x0001d32651aa3f249c583b9cd4477c4e7db1e66df7a751f3ad15b14c60231d4f",
  "0x007c9473decf64fc76c4166dc92a3a40ae683d8ca4eb47d0761d66d8a454d5de",
  "0x0061f1c2ac928c6b927969017d0d014ab743382d0036f723eb66cbb0ba7a949b",
  "0x00f7624413bf4f3dbab37feef5bf4073cae6b4e66fbb70cb314c0d4613a8fc97",
  "0x00dad83820e359d3602fb6522c3b5aa6cc7f8c7fd040987787131a24e85bfa89",
  "0x007d34b1444fdac84689ccb22e5e25f02617b50ad1c965ea6e6c324efe0ef9d6",
  "0x0037c35077b06e5abd08f21a838a9e077cb45df5805fe6dae6285ea223c91db4",
  "0x00b1211e908a8fe8ecddffc5ec8b8826d62f508da9f2a66153d870c3136414ea",
  "0x00ed555e429fa113e983ac58165c3d1370662033a62ce055ba7e37e2dc987e67",
  "0x00e1d84b7ad9ef6d9ac4318f3f0b5a4cfdd6d3a31d5311f1fb2514e0f301bb91",
  "0x000930b9b59a600b19d37fb2779c6bcdf10f7c2198b9c8c16b1b38ebeac3e64f",
  "0x0049cb4ddcc40bb4dbe8faa0c46848d89cf9796f952c32224e1d9e294f702fc6",
  "0x003e9735d144983f427e32ee3894f5286f1c08b205fee4a74452951d45efe2f0",
  "0x006f4ab6c665f61b259bdfce5c7c1f9ae1248bb1d37995992fc70c2a3048ba83",
  "0x0057c0ff2429a79af79a3580aeefecfd7981da7c3524c95149b2bcae78e51879",
  "0x0082b19e794ac9ef46aed0c6afa0e8c18777923c5766b26d173c21c7e6ca8358"
]
    length = "0x0000000000000000000000000000000000000000000000000000000000000012"

  [[app_public_inputs.private_logs]]
  note_hash_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [app_public_inputs.private_logs.log]
    fields = [
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000"
]
    length = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.private_logs]]
  note_hash_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [app_public_inputs.private_logs.log]
    fields = [
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000"
]
    length = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.private_logs]]
  note_hash_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [app_public_inputs.private_logs.log]
    fields = [
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000"
]
    length = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.private_logs]]
  note_hash_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [app_public_inputs.private_logs.log]
    fields = [
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000"
]
    length = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.private_logs]]
  note_hash_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [app_public_inputs.private_logs.log]
    fields = [
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000"
]
    length = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.private_logs]]
  note_hash_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [app_public_inputs.private_logs.log]
    fields = [
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000"
]
    length = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.private_logs]]
  note_hash_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [app_public_inputs.private_logs.log]
    fields = [
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000"
]
    length = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.private_logs]]
  note_hash_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [app_public_inputs.private_logs.log]
    fields = [
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000"
]
    length = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.private_logs]]
  note_hash_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [app_public_inputs.private_logs.log]
    fields = [
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000"
]
    length = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.private_logs]]
  note_hash_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [app_public_inputs.private_logs.log]
    fields = [
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000"
]
    length = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.private_logs]]
  note_hash_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [app_public_inputs.private_logs.log]
    fields = [
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000"
]
    length = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.private_logs]]
  note_hash_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [app_public_inputs.private_logs.log]
    fields = [
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000"
]
    length = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.private_logs]]
  note_hash_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [app_public_inputs.private_logs.log]
    fields = [
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000"
]
    length = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.contract_class_logs_hashes]]
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [app_public_inputs.contract_class_logs_hashes.inner]
    value = "0x0000000000000000000000000000000000000000000000000000000000000000"
    length = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [app_public_inputs.historical_header]
  total_fees = "0x00000000000000000000000000000000000000000000000000000000741958c4"
  total_mana_used = "0x000000000000000000000000000000000000000000000000000000000000d23e"

    [app_public_inputs.historical_header.last_archive]
    root = "0x07d03443b8719aac8b4b54ab132940fc21800149845bd28697ed3baeaa21a523"
    next_available_leaf_index = "0x0000000000000000000000000000000000000000000000000000000000000006"

    [app_public_inputs.historical_header.content_commitment]
    blobs_hash = "0x000c0a16f870bff67605e8c18de39068658cbafa5444fff90752364563815ec6"
    in_hash = "0x00089a9d421a82c4a25f7acbebe69e638d5b064fa8a60e018793dcb0be53752c"
    out_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

[app_public_inputs.historical_header.state.l1_to_l2_message_tree]
root = "0x2e33ee2008411c04b99c24b313513d097a0d21a5040b6193d1f978b8226892d6"
next_available_leaf_index = "0x0000000000000000000000000000000000000000000000000000000000000060"

[app_public_inputs.historical_header.state.partial.note_hash_tree]
root = "0x10c1f741a0fa706e1eddb3bf23cb6f1305bfb60d621af66f5427d0d67d0b312a"
next_available_leaf_index = "0x0000000000000000000000000000000000000000000000000000000000000180"

[app_public_inputs.historical_header.state.partial.nullifier_tree]
root = "0x0e4db848d4729ffb24a540d50688a3db38e38460da4eadd9a13308b2405ec6d9"
next_available_leaf_index = "0x0000000000000000000000000000000000000000000000000000000000000200"

[app_public_inputs.historical_header.state.partial.public_data_tree]
root = "0x1fcc6dea40abf7522d330bc6c1d187fc476f4a3c3af9f16d9dc2c4c0cb8ff092"
next_available_leaf_index = "0x000000000000000000000000000000000000000000000000000000000000008a"

    [app_public_inputs.historical_header.global_variables]
    chain_id = "0x0000000000000000000000000000000000000000000000000000000000007a69"
    version = "0x0000000000000000000000000000000000000000000000000000000055c58b0d"
    block_number = "0x0000000000000000000000000000000000000000000000000000000000000006"
    slot_number = "0x0000000000000000000000000000000000000000000000000000000000000007"
    timestamp = "0x00000000000000000000000000000000000000000000000000000000684882f2"

      [app_public_inputs.historical_header.global_variables.coinbase]
      inner = "0x0000000000000000000000008e7508851cd7c32bc45138c6315abb45e66adbbb"

      [app_public_inputs.historical_header.global_variables.fee_recipient]
      inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

      [app_public_inputs.historical_header.global_variables.gas_fees]
      fee_per_da_gas = "0x0000000000000000000000000000000000000000000000000000000000000000"
      fee_per_l2_gas = "0x0000000000000000000000000000000000000000000000000000000000008d5e"

  [app_public_inputs.tx_context]
  chain_id = "0x0000000000000000000000000000000000000000000000000000000000007a69"
  version = "0x0000000000000000000000000000000000000000000000000000000055c58b0d"

[app_public_inputs.tx_context.gas_settings.gas_limits]
da_gas = "0x000000000000000000000000000000000000000000000000000000003b9aca00"
l2_gas = "0x000000000000000000000000000000000000000000000000000000003b9aca00"

[app_public_inputs.tx_context.gas_settings.teardown_gas_limits]
da_gas = "0x00000000000000000000000000000000000000000000000000000000005b8d80"
l2_gas = "0x00000000000000000000000000000000000000000000000000000000005b8d80"

[app_public_inputs.tx_context.gas_settings.max_fees_per_gas]
fee_per_da_gas = "0x0000000000000000000000000000000000000000000000000000000000000000"
fee_per_l2_gas = "0x000000000000000000000000000000000000000000000000000000000000d40d"

[app_public_inputs.tx_context.gas_settings.max_priority_fees_per_gas]
fee_per_da_gas = "0x0000000000000000000000000000000000000000000000000000000000000000"
fee_per_l2_gas = "0x0000000000000000000000000000000000000000000000000000000000000000"
