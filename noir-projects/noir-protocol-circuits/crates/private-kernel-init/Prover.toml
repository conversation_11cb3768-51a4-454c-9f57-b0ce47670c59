vk_tree_root = "0x1ee6a40f9c295dbdbcaf4eafcba42c488330caff248419f6a0100ed9d5b2719c"
protocol_contract_tree_root = "0x0dbfba3eddc8d0560547d65dac94beb59c45bb56de9df596e338505b620fa335"
is_private_only = false
first_nullifier_hint = "0x0000000000000000000000000000000000000000000000000000000000000000"

[tx_request]
args_hash = "0x27d7f01cc1a47f4bd7ef3323f5843af63ef366825dd2953b3ce1e29731ac8385"
salt = "0x1476bbd7469cf8a3a1825c254da90c6fcf1511a72dbca876d9549e9098dd07f6"

  [tx_request.origin]
  inner = "0x2bd6622f715bd307d4918e462bd9aa829794c19bfe8ebbe5390237361e65e437"

  [tx_request.tx_context]
  chain_id = "0x0000000000000000000000000000000000000000000000000000000000007a69"
  version = "0x0000000000000000000000000000000000000000000000000000000055c58b0d"

[tx_request.tx_context.gas_settings.gas_limits]
da_gas = "0x000000000000000000000000000000000000000000000000000000003b9aca00"
l2_gas = "0x000000000000000000000000000000000000000000000000000000003b9aca00"

[tx_request.tx_context.gas_settings.teardown_gas_limits]
da_gas = "0x00000000000000000000000000000000000000000000000000000000005b8d80"
l2_gas = "0x00000000000000000000000000000000000000000000000000000000005b8d80"

[tx_request.tx_context.gas_settings.max_fees_per_gas]
fee_per_da_gas = "0x0000000000000000000000000000000000000000000000000000000000000000"
fee_per_l2_gas = "0x000000000000000000000000000000000000000000000000000000000000d40d"

[tx_request.tx_context.gas_settings.max_priority_fees_per_gas]
fee_per_da_gas = "0x0000000000000000000000000000000000000000000000000000000000000000"
fee_per_l2_gas = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [tx_request.function_data]
  is_private = true

    [tx_request.function_data.selector]
    inner = "0x0000000000000000000000000000000000000000000000000000000027e740b2"

[private_call.vk]
key = [
  "0x0000000000000000000000000000000000000000000000000000000000040000",
  "0x0000000000000000000000000000000000000000000000000000000000000010",
  "0x0000000000000000000000000000000000000000000000000000000000005609",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x00000000000000000000000000000000000000000000000000000000ffffffff",
  "0x00000000000000000000000000000000000000000000000000000000ffffffff",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000005b72920c78292b7c642775f9c99f52c0d5",
  "0x000000000000000000000000000000000020da70fc5205246c0450e2156fd033",
  "0x000000000000000000000000000000fb5771901705c402a277ccab6c22a62686",
  "0x00000000000000000000000000000000002bce91dd1a3a3955342ef6c36643e7",
  "0x00000000000000000000000000000040f8a7de3bc8188365f34175fddbd9f8d8",
  "0x00000000000000000000000000000000001d0b914cdb2fcd67e13b62edccfe33",
  "0x000000000000000000000000000000cf2af98d3f3a79958865b3badd978da5e3",
  "0x000000000000000000000000000000000028e732016dea4edfd49455063acea8",
  "0x00000000000000000000000000000091ba6e54dd80306a55a2a56002c73298e2",
  "0x000000000000000000000000000000000027cd8c3f3a67d83c2b1beff948b710",
  "0x0000000000000000000000000000004b9a0099ad915d5d43e429fb150db93714",
  "0x00000000000000000000000000000000001e4bdc792494ba8772accd7dde846c",
  "0x000000000000000000000000000000ae219e79d602a886d1a308a9013018220f",
  "0x000000000000000000000000000000000028bdc6c50bcd1d63a3e8f93c079889",
  "0x000000000000000000000000000000f7288aede6e47e73d6029452b8d3f8ba69",
  "0x0000000000000000000000000000000000212995deccca87f41a4324a8598c39",
  "0x000000000000000000000000000000cd260dc1bf9423b451dba66523515b033a",
  "0x000000000000000000000000000000000006ed4c69849530cd7977546dc989d9",
  "0x000000000000000000000000000000bcedc66a5f4dabc3af0ef45782c206a5cd",
  "0x00000000000000000000000000000000001eb95276955cc488158b64fc838e1c",
  "0x00000000000000000000000000000044da73ba01f4e5d65c0922560aaa497ef3",
  "0x000000000000000000000000000000000013941a4e2b5a2c33e3dac4534a3c8b",
  "0x00000000000000000000000000000019fe350524a2aaf567f4fb5679c3f0eeee",
  "0x00000000000000000000000000000000002a9dc0186ce2613e9745b918d4afb1",
  "0x0000000000000000000000000000000fd9156afc5984d7e2b218538e73a36ab3",
  "0x000000000000000000000000000000000011a6d5bc8a5f54ad429a8d2c202bd1",
  "0x00000000000000000000000000000072031e3e7b40ddb9a8adca76f6788398c5",
  "0x0000000000000000000000000000000000046871b27b69ac58ad6387976d0eda",
  "0x000000000000000000000000000000d0a00080cd8f34ba0e37546c102665c8fe",
  "0x00000000000000000000000000000000000d56bbef147278fdc057f9a336d984",
  "0x000000000000000000000000000000f11f3eaed8726026211d2ee0f83e32e453",
  "0x0000000000000000000000000000000000291fbbe0b7f6f2823d5469cf981a1e",
  "0x000000000000000000000000000000bc6ede84d038829d5b2593e468c94ba66a",
  "0x00000000000000000000000000000000000d718389c462d39c0a11fc9621370d",
  "0x0000000000000000000000000000007d5110f399df429e37da5a2a4d0b600666",
  "0x0000000000000000000000000000000000302b437cf116802f89aca0270b5fa8",
  "0x0000000000000000000000000000007c84ddfd2981ccc60b137abfbdd81d540f",
  "0x00000000000000000000000000000000000b1cc348db97509ebef07c055a0a4c",
  "0x00000000000000000000000000000008bdff388bc6e22055be0165e5ded329e7",
  "0x000000000000000000000000000000000017fd6ecb2cc3749219c4ec61241fe5",
  "0x000000000000000000000000000000909105267394e8d6cd9ac5a891eeef6370",
  "0x00000000000000000000000000000000002ed97daa7ed6902a6c303391a5b301",
  "0x000000000000000000000000000000dad2e40e7375ce93eb2f6ea5d11681449c",
  "0x00000000000000000000000000000000001e1436044c8d1ff1a43c1abc5092a5",
  "0x0000000000000000000000000000001f3e02d193e08f48e8cda7980ee69aa41c",
  "0x000000000000000000000000000000000016cf9d30048eeb548a401f8a0525cf",
  "0x0000000000000000000000000000008e15507df6461abe076cbd08b5cb573e8a",
  "0x000000000000000000000000000000000006317ef1022d23a76f5613052f0150",
  "0x00000000000000000000000000000068c38b1cbbc187ed876db29119b21fefc9",
  "0x000000000000000000000000000000000011b6989170787d0ec6e790bbf44f2e",
  "0x0000000000000000000000000000001d84d583f8b9223fe3deda1752f94ee111",
  "0x00000000000000000000000000000000001d4e221a563f093c6f3a6202a1bb16",
  "0x0000000000000000000000000000006051cef375a22a225c8d65dec619a67a38",
  "0x0000000000000000000000000000000000110b407173b1ea857c1474ae3d2f6b",
  "0x000000000000000000000000000000ec2ae3a1f0435e087188ac7d6e45a1ae7d",
  "0x000000000000000000000000000000000010d8a71d9e49c33a30ad24ec50ca19",
  "0x000000000000000000000000000000a0c04f61a203e2e60e7b33f10a0d13b76b",
  "0x000000000000000000000000000000000014af804ad308e837547429020c192e",
  "0x00000000000000000000000000000073a169cf54d502801da3f70d33bd55fd1b",
  "0x00000000000000000000000000000000000df9b5e7940b02cbe8446373fc48fb",
  "0x000000000000000000000000000000631b96f3e471da1e47bfe0a999d04c1397",
  "0x000000000000000000000000000000000006b01518e6b8093ff130984817debd",
  "0x000000000000000000000000000000fe5b077cb726eec31fc921436a2ef8ec60",
  "0x000000000000000000000000000000000018bf841838bf98fbf8e2f65a221470",
  "0x000000000000000000000000000000a01e285f6db6eb0a632525bef1e902717b",
  "0x00000000000000000000000000000000001efc626183766f1592cf8ec593f3a7",
  "0x0000000000000000000000000000004920b365be6fd19c275de3e1f218b1d335",
  "0x00000000000000000000000000000000000c2f4c1bde4b94bb4636055653cb99",
  "0x0000000000000000000000000000009c3e0f11036450585b6422beeffed70b3d",
  "0x00000000000000000000000000000000000e04bd094a2d29ddb87b45dbb2fb8f",
  "0x00000000000000000000000000000082a88dc30bef0f6d8cf707913fa11158fd",
  "0x000000000000000000000000000000000027adaff2dadea4544b954995d82910",
  "0x000000000000000000000000000000f7d006882c73d51fd9354f046a1a5da611",
  "0x000000000000000000000000000000000013319ba87dbbeb2e5a8775605d780d",
  "0x00000000000000000000000000000020216b10135d16f64f5a7f0c9472a4f81f",
  "0x000000000000000000000000000000000020a03fe094dd1e7ec2b6b9947de363",
  "0x00000000000000000000000000000056da0aec76aaab226a6acad4127328f0e7",
  "0x000000000000000000000000000000000022246103e1ba4c31691b026ed4d91d",
  "0x0000000000000000000000000000001046f298821ca396bca6a088bf964f3db6",
  "0x000000000000000000000000000000000018b44c10f3014e171f2229a1a2b110",
  "0x000000000000000000000000000000fd07f47b045882bbc0392016023cade674",
  "0x00000000000000000000000000000000001d9e32deaaf92833595b95f3cfe5bd",
  "0x000000000000000000000000000000c31debd4f009bdf7b1bb175a0c05aff2a7",
  "0x00000000000000000000000000000000000fb09c90eaf02cdcf2bb8bee4e7917",
  "0x000000000000000000000000000000a275ad3aeffece26decda93934468bf0a9",
  "0x0000000000000000000000000000000000196244fc54897bc36afb3e73e8168d",
  "0x000000000000000000000000000000167150b8a369cbdf3ecc22a68ca45b3c5d",
  "0x000000000000000000000000000000000025b14a58e09e6f2703d0872ae064af",
  "0x00000000000000000000000000000084a9b3527cd2ebff62b245a04aab258f92",
  "0x00000000000000000000000000000000000a85019e1252699312cbd5ec6a23b2",
  "0x00000000000000000000000000000000b5eee72336430c3feb7da6b8b57e1551",
  "0x00000000000000000000000000000000001bba1a6e49f0ba66643e8b32fd090e",
  "0x0000000000000000000000000000004d612c80fc91a269aaaaa975c84c8e58d6",
  "0x000000000000000000000000000000000000b34e0082bc5aed819a81bb36744c",
  "0x000000000000000000000000000000beb969e0f2c7856270dc5fda2c5d399dcc",
  "0x00000000000000000000000000000000000013aea3bcc0841ec6d94b285f1beb",
  "0x0000000000000000000000000000002671782a93372aad369530ae4b75c22bdb",
  "0x000000000000000000000000000000000002d9f0465ef4b2b116d4b88625344f",
  "0x00000000000000000000000000000026e9839942d72920141febdab07e4c20c3",
  "0x0000000000000000000000000000000000265f0c70536ec02f7c9be4ce19c29d",
  "0x0000000000000000000000000000003a339e8cb8c648d07c34ddcb4ef4452783",
  "0x000000000000000000000000000000000027807a4f7b23d9cc1c865ef9930999",
  "0x000000000000000000000000000000bc4fd810c781d7b239a47a086361686edc",
  "0x00000000000000000000000000000000000cbf9d6e0b6faa609ddbd5817f5d10",
  "0x0000000000000000000000000000000000000000000000000000000000000001",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000002",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x000000000000000000000000000000ae20cb6dc8874f7723ac21f5c5984c3297",
  "0x00000000000000000000000000000000001835527ffc970b894780b151db88ec",
  "0x000000000000000000000000000000475f306e9003dd921fe51ea3e3e749fbe9",
  "0x00000000000000000000000000000000000cc146ff4e30b5e13493ebbe8a72c0",
  "0x0000000000000000000000000000006bcc7a05ff95a96b289424c5f733670d96",
  "0x000000000000000000000000000000000000c43726f75b6fda0de22ce0e0dfab",
  "0x0000000000000000000000000000001d0a09d7178ec93bad7858f96e64f0b48d",
  "0x00000000000000000000000000000000002f9b6e0b4e2c01968de5c32482aa7d",
  "0x000000000000000000000000000000e55ba19751adfe6c36324d3fb6c2da0989",
  "0x00000000000000000000000000000000001d58aa61c64ad522043d79c4802219",
  "0x00000000000000000000000000000078f4b3bc61f19d6e7069359bbf47e7f907",
  "0x00000000000000000000000000000000002d7c18a93c3dae58809faaeec6a86a"
]
hash = "0x234b6cf90a80117b12ae53cda35a7a52860e211e71b441f7e8c4b1341a1801bb"

[private_call.verification_key_hints]
contract_class_artifact_hash = "0x28e7f99a528d8df5707f571c61086b4ab2e5eaf11133d11b8cf5f59566ce59a8"
contract_class_public_bytecode_commitment = "0x136df56b2c789090458197f8589ee9a9215579138440b4fe7cd9d33e8d88151c"
updated_class_id_shared_mutable_values = [
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000"
]

  [private_call.verification_key_hints.function_leaf_membership_witness]
  leaf_index = "0"
  sibling_path = [
  "0x018fdbe28502de04e327a199d19684b8d02de05bf0643f1ea0005400fca7e7fd",
  "0x2631fa1f38b991dd08464a47a630de4f3c86f349fa5e87db939370b16046166b",
  "0x0e1ce4f11f4d51a7d3136abbd625315fff3876d322e46ad8401da96f8e43a614",
  "0x1a0ca5eecb1430479902264f04e557160a4666fb42bb8b283c6397e3d17ac937",
  "0x2a6595890719fef7967c209488728aa5342438ba52058a3c770202f55acf6854"
]

[private_call.verification_key_hints.public_keys.npk_m.inner]
x = "0x28dd2b7fddec890b822ca683d378afc9a5c40082602bf935c0a996d5a477043b"
y = "0x23ff8c1cb51f4e90e29b7650d95714626eca1f7880692b116891fae901e42217"
is_infinite = false

[private_call.verification_key_hints.public_keys.ivpk_m.inner]
x = "0x00b73f568b3b60d352230dc9f15f4d2e1c7a6a79e83863fa9d183ca74c350602"
y = "0x04b7877ebc5b88f7f55660197abf933d9b86f9c33f9f05abd808d96a00c33b1f"
is_infinite = false

[private_call.verification_key_hints.public_keys.ovpk_m.inner]
x = "0x07c9dd7642967d004ddcb5f7d3f888bb5a3507326cb230b8ceb715ab7fcaf53b"
y = "0x0578856d487a7955e7b4011caff404ee23d9bc6a2cefaf2a875e7a978d92edf2"
is_infinite = false

[private_call.verification_key_hints.public_keys.tpk_m.inner]
x = "0x00d68a8a9b7a104caf8ccb2873842e8d8f9b6f0d96e619a413ff9a1fd1cd3293"
y = "0x1d6cc34289863a166fff9cc16795b88a9d9ae7664249000bbac55693dece228b"
is_infinite = false

  [private_call.verification_key_hints.salted_initialization_hash]
  inner = "0x2c38565c02ef0bc647e0ccae630a4056eeab5b36ddaf7216a6f80206e07642f4"

  [private_call.verification_key_hints.protocol_contract_membership_witness]
  leaf_index = "5"
  sibling_path = [
  "0x2bcd6da5c9cf56c2dbac35dcbfc20a8fd15051f81577c32f489e5e81c6eecfe8",
  "0x1e4dfdec09cf3cf09e50a2b5dd79fc3625f4f26744a15437b7df2f8e718b6d8f",
  "0x1ae7aba6971a2bb72fcf70e1376077b8f3c30441a8fabf4ddb29c599e539d4b7"
]

  [private_call.verification_key_hints.protocol_contract_leaf]
  address = "0x212209c22c967eecc546657e4fdcfdb0652dcff93b6a3d906b26cc1664c3829b"
  next_address = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [private_call.verification_key_hints.updated_class_id_witness]
  leaf_index = "136"
  sibling_path = [
  "0x16f8e8532d33c1537cd08addca169cc107bd07ec842297578a9a8dd838ba4221",
  "0x0b63a53787021a4a962a452c2921b3663aff1ffd8d5510540f8e659e782956f1",
  "0x0e34ac2c09f45a503d2908bcb12f1cbae5fa4065759c88d501c097506a8b2290",
  "0x0937f2f7f0fe34246c985eacfb4681bce5cc87a8d63c770f4d86883abcf96324",
  "0x2373ea368857ec7af97e7b470d705848e2bf93ed7bef142a490f2119bcf82d8e",
  "0x120157cfaaa49ce3da30f8b47879114977c24b266d58b0ac18b325d878aafddf",
  "0x01c28fe1059ae0237b72334700697bdf465e03df03986fe05200cadeda66bd76",
  "0x2dbb51a7e0e06fe0ff6417d3052d11735ba22d33afd7424512bcb079915543bb",
  "0x067243231eddf4222f3911defbba7705aff06ed45960b27f6f91319196ef97e1",
  "0x1849b85f3c693693e732dfc4577217acc18295193bede09ce8b97ad910310972",
  "0x2a775ea761d20435b31fa2c33ff07663e24542ffb9e7b293dfce3042eb104686",
  "0x0f320b0703439a8114f81593de99cd0b8f3b9bf854601abb5b2ea0e8a3dda4a7",
  "0x0d07f6e7a8a0e9199d6d92801fff867002ff5b4808962f9da2ba5ce1bdd26a73",
  "0x1c4954081e324939350febc2b918a293ebcdaead01be95ec02fcbe8d2c1635d1",
  "0x0197f2171ef99c2d053ee1fb5ff5ab288d56b9b41b4716c9214a4d97facc4c4a",
  "0x2b9cdd484c5ba1e4d6efcc3f18734b5ac4c4a0b9102e2aeb48521a661d3feee9",
  "0x14f44d672eb357739e42463497f9fdac46623af863eea4d947ca00a497dcdeb3",
  "0x071d7627ae3b2eabda8a810227bf04206370ac78dbf6c372380182dbd3711fe3",
  "0x2fdc08d9fe075ac58cb8c00f98697861a13b3ab6f9d41a4e768f75e477475bf5",
  "0x20165fe405652104dceaeeca92950aa5adc571b8cafe192878cba58ff1be49c5",
  "0x1c8c3ca0b3a3d75850fcd4dc7bf1e3445cd0cfff3ca510630fd90b47e8a24755",
  "0x1f0c1a8fb16b0d2ac9a146d7ae20d8d179695a92a79ed66fc45d9da4532459b3",
  "0x038146ec5a2573e1c30d2fb32c66c8440f426fbd108082df41c7bebd1d521c30",
  "0x17d3d12b17fe762de4b835b2180b012e808816a7f2ff69ecb9d65188235d8fd4",
  "0x0e1a6b7d63a6e5a9e54e8f391dd4e9d49cdfedcbc87f02cd34d4641d2eb30491",
  "0x09244eec34977ff795fc41036996ce974136377f521ac8eb9e04642d204783d2",
  "0x1646d6f544ec36df9dc41f778a7ef1690a53c730b501471b6acd202194a7e8e9",
  "0x064769603ba3f6c41f664d266ecb9a3a0f6567cd3e48b40f34d4894ee4c361b3",
  "0x1595bb3cd19f84619dc2e368175a88d8627a7439eda9397202cdb1167531fd3f",
  "0x2a529be462b81ca30265b558763b1498289c9d88277ab14f0838cb1fce4b472c",
  "0x0c08da612363088ad0bbc78abd233e8ace4c05a56fdabdd5e5e9b05e428bdaee",
  "0x14748d0241710ef47f54b931ac5a58082b1d56b0f0c30d55fb71a6e8c9a6be14",
  "0x0b59baa35b9dc267744f0ccb4e3b0255c1fc512460d91130c6bc19fb2668568d",
  "0x2c45bb0c3d5bc1dc98e0baef09ff46d18c1a451e724f41c2b675549bb5c80e59",
  "0x121468e6710bf1ffec6d0f26743afe6f88ef55dab40b83ca0a39bc44b196374c",
  "0x2042c32c823a7440ceb6c342f9125f1fe426b02c527cd8fb28c85d02b705e759",
  "0x0d582c10ff8115413aa5b70564fdd2f3cefe1f33a1e43a47bc495081e91e73e5",
  "0x0f55a0d491a9da093eb999fa0dffaf904620cbc78d07e63c6f795c5c7512b523",
  "0x21849764e1aa64b83a69e39d27eedaec2a8f97066e5ddb74634ffdb11388dd9a",
  "0x2e33ee2008411c04b99c24b313513d097a0d21a5040b6193d1f978b8226892d6"
]

  [private_call.verification_key_hints.updated_class_id_leaf]
  slot = "0x2e4b74cd662958246772becbff96e3862e774ae1c00c32b1a6c52ac22f7ff3f4"
  value = "0x0000000000000000000000000000000000000000000000000000000000002710"
  next_slot = "0x0000000000000000000000000000000000000000000000000000000000000000"
  next_index = "0x0000000000000000000000000000000000000000000000000000000000000000"

[app_public_inputs]
args_hash = "0x27d7f01cc1a47f4bd7ef3323f5843af63ef366825dd2953b3ce1e29731ac8385"
returns_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"
start_side_effect_counter = "0x0000000000000000000000000000000000000000000000000000000000000001"
end_side_effect_counter = "0x0000000000000000000000000000000000000000000000000000000000000004"
min_revertible_side_effect_counter = "0x0000000000000000000000000000000000000000000000000000000000000003"
is_fee_payer = true

[app_public_inputs.max_block_number._opt]
_is_some = false
_value = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [app_public_inputs.call_context]
  is_static_call = false

    [app_public_inputs.call_context.msg_sender]
    inner = "0x30644e72e131a029b85045b68181585d2833e84879b9709143e1f593f0000000"

    [app_public_inputs.call_context.contract_address]
    inner = "0x2bd6622f715bd307d4918e462bd9aa829794c19bfe8ebbe5390237361e65e437"

    [app_public_inputs.call_context.function_selector]
    inner = "0x0000000000000000000000000000000000000000000000000000000027e740b2"

  [[app_public_inputs.note_hash_read_requests]]
  value = "0x2a40d7e8d09b7119d5950f4bb67494f83511bbefb12c017077dc480abdd229ac"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000002"

  [[app_public_inputs.note_hash_read_requests]]
  value = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.note_hash_read_requests]]
  value = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.note_hash_read_requests]]
  value = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.note_hash_read_requests]]
  value = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.note_hash_read_requests]]
  value = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.note_hash_read_requests]]
  value = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.note_hash_read_requests]]
  value = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.note_hash_read_requests]]
  value = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.note_hash_read_requests]]
  value = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.note_hash_read_requests]]
  value = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.note_hash_read_requests]]
  value = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.note_hash_read_requests]]
  value = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.note_hash_read_requests]]
  value = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.note_hash_read_requests]]
  value = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.note_hash_read_requests]]
  value = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.nullifier_read_requests]]
  value = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.nullifier_read_requests]]
  value = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.nullifier_read_requests]]
  value = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.nullifier_read_requests]]
  value = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.nullifier_read_requests]]
  value = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.nullifier_read_requests]]
  value = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.nullifier_read_requests]]
  value = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.nullifier_read_requests]]
  value = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.nullifier_read_requests]]
  value = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.nullifier_read_requests]]
  value = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.nullifier_read_requests]]
  value = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.nullifier_read_requests]]
  value = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.nullifier_read_requests]]
  value = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.nullifier_read_requests]]
  value = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.nullifier_read_requests]]
  value = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.nullifier_read_requests]]
  value = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.key_validation_requests_and_generators]]
  sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [app_public_inputs.key_validation_requests_and_generators.request]
    sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

      [app_public_inputs.key_validation_requests_and_generators.request.pk_m]
      x = "0x0000000000000000000000000000000000000000000000000000000000000000"
      y = "0x0000000000000000000000000000000000000000000000000000000000000000"
      is_infinite = false

  [[app_public_inputs.key_validation_requests_and_generators]]
  sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [app_public_inputs.key_validation_requests_and_generators.request]
    sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

      [app_public_inputs.key_validation_requests_and_generators.request.pk_m]
      x = "0x0000000000000000000000000000000000000000000000000000000000000000"
      y = "0x0000000000000000000000000000000000000000000000000000000000000000"
      is_infinite = false

  [[app_public_inputs.key_validation_requests_and_generators]]
  sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [app_public_inputs.key_validation_requests_and_generators.request]
    sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

      [app_public_inputs.key_validation_requests_and_generators.request.pk_m]
      x = "0x0000000000000000000000000000000000000000000000000000000000000000"
      y = "0x0000000000000000000000000000000000000000000000000000000000000000"
      is_infinite = false

  [[app_public_inputs.key_validation_requests_and_generators]]
  sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [app_public_inputs.key_validation_requests_and_generators.request]
    sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

      [app_public_inputs.key_validation_requests_and_generators.request.pk_m]
      x = "0x0000000000000000000000000000000000000000000000000000000000000000"
      y = "0x0000000000000000000000000000000000000000000000000000000000000000"
      is_infinite = false

  [[app_public_inputs.key_validation_requests_and_generators]]
  sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [app_public_inputs.key_validation_requests_and_generators.request]
    sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

      [app_public_inputs.key_validation_requests_and_generators.request.pk_m]
      x = "0x0000000000000000000000000000000000000000000000000000000000000000"
      y = "0x0000000000000000000000000000000000000000000000000000000000000000"
      is_infinite = false

  [[app_public_inputs.key_validation_requests_and_generators]]
  sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [app_public_inputs.key_validation_requests_and_generators.request]
    sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

      [app_public_inputs.key_validation_requests_and_generators.request.pk_m]
      x = "0x0000000000000000000000000000000000000000000000000000000000000000"
      y = "0x0000000000000000000000000000000000000000000000000000000000000000"
      is_infinite = false

  [[app_public_inputs.key_validation_requests_and_generators]]
  sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [app_public_inputs.key_validation_requests_and_generators.request]
    sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

      [app_public_inputs.key_validation_requests_and_generators.request.pk_m]
      x = "0x0000000000000000000000000000000000000000000000000000000000000000"
      y = "0x0000000000000000000000000000000000000000000000000000000000000000"
      is_infinite = false

  [[app_public_inputs.key_validation_requests_and_generators]]
  sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [app_public_inputs.key_validation_requests_and_generators.request]
    sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

      [app_public_inputs.key_validation_requests_and_generators.request.pk_m]
      x = "0x0000000000000000000000000000000000000000000000000000000000000000"
      y = "0x0000000000000000000000000000000000000000000000000000000000000000"
      is_infinite = false

  [[app_public_inputs.key_validation_requests_and_generators]]
  sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [app_public_inputs.key_validation_requests_and_generators.request]
    sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

      [app_public_inputs.key_validation_requests_and_generators.request.pk_m]
      x = "0x0000000000000000000000000000000000000000000000000000000000000000"
      y = "0x0000000000000000000000000000000000000000000000000000000000000000"
      is_infinite = false

  [[app_public_inputs.key_validation_requests_and_generators]]
  sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [app_public_inputs.key_validation_requests_and_generators.request]
    sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

      [app_public_inputs.key_validation_requests_and_generators.request.pk_m]
      x = "0x0000000000000000000000000000000000000000000000000000000000000000"
      y = "0x0000000000000000000000000000000000000000000000000000000000000000"
      is_infinite = false

  [[app_public_inputs.key_validation_requests_and_generators]]
  sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [app_public_inputs.key_validation_requests_and_generators.request]
    sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

      [app_public_inputs.key_validation_requests_and_generators.request.pk_m]
      x = "0x0000000000000000000000000000000000000000000000000000000000000000"
      y = "0x0000000000000000000000000000000000000000000000000000000000000000"
      is_infinite = false

  [[app_public_inputs.key_validation_requests_and_generators]]
  sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [app_public_inputs.key_validation_requests_and_generators.request]
    sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

      [app_public_inputs.key_validation_requests_and_generators.request.pk_m]
      x = "0x0000000000000000000000000000000000000000000000000000000000000000"
      y = "0x0000000000000000000000000000000000000000000000000000000000000000"
      is_infinite = false

  [[app_public_inputs.key_validation_requests_and_generators]]
  sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [app_public_inputs.key_validation_requests_and_generators.request]
    sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

      [app_public_inputs.key_validation_requests_and_generators.request.pk_m]
      x = "0x0000000000000000000000000000000000000000000000000000000000000000"
      y = "0x0000000000000000000000000000000000000000000000000000000000000000"
      is_infinite = false

  [[app_public_inputs.key_validation_requests_and_generators]]
  sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [app_public_inputs.key_validation_requests_and_generators.request]
    sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

      [app_public_inputs.key_validation_requests_and_generators.request.pk_m]
      x = "0x0000000000000000000000000000000000000000000000000000000000000000"
      y = "0x0000000000000000000000000000000000000000000000000000000000000000"
      is_infinite = false

  [[app_public_inputs.key_validation_requests_and_generators]]
  sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [app_public_inputs.key_validation_requests_and_generators.request]
    sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

      [app_public_inputs.key_validation_requests_and_generators.request.pk_m]
      x = "0x0000000000000000000000000000000000000000000000000000000000000000"
      y = "0x0000000000000000000000000000000000000000000000000000000000000000"
      is_infinite = false

  [[app_public_inputs.key_validation_requests_and_generators]]
  sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [app_public_inputs.key_validation_requests_and_generators.request]
    sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

      [app_public_inputs.key_validation_requests_and_generators.request.pk_m]
      x = "0x0000000000000000000000000000000000000000000000000000000000000000"
      y = "0x0000000000000000000000000000000000000000000000000000000000000000"
      is_infinite = false

  [[app_public_inputs.note_hashes]]
  value = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.note_hashes]]
  value = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.note_hashes]]
  value = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.note_hashes]]
  value = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.note_hashes]]
  value = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.note_hashes]]
  value = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.note_hashes]]
  value = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.note_hashes]]
  value = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.note_hashes]]
  value = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.note_hashes]]
  value = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.note_hashes]]
  value = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.note_hashes]]
  value = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.note_hashes]]
  value = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.note_hashes]]
  value = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.note_hashes]]
  value = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.note_hashes]]
  value = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.nullifiers]]
  value = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
  note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.nullifiers]]
  value = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
  note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.nullifiers]]
  value = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
  note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.nullifiers]]
  value = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
  note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.nullifiers]]
  value = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
  note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.nullifiers]]
  value = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
  note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.nullifiers]]
  value = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
  note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.nullifiers]]
  value = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
  note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.nullifiers]]
  value = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
  note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.nullifiers]]
  value = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
  note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.nullifiers]]
  value = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
  note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.nullifiers]]
  value = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
  note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.nullifiers]]
  value = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
  note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.nullifiers]]
  value = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
  note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.nullifiers]]
  value = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
  note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.nullifiers]]
  value = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
  note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.private_call_requests]]
  args_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"
  returns_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"
  start_side_effect_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
  end_side_effect_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [app_public_inputs.private_call_requests.call_context]
    is_static_call = false

      [app_public_inputs.private_call_requests.call_context.msg_sender]
      inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

      [app_public_inputs.private_call_requests.call_context.contract_address]
      inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

      [app_public_inputs.private_call_requests.call_context.function_selector]
      inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.private_call_requests]]
  args_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"
  returns_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"
  start_side_effect_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
  end_side_effect_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [app_public_inputs.private_call_requests.call_context]
    is_static_call = false

      [app_public_inputs.private_call_requests.call_context.msg_sender]
      inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

      [app_public_inputs.private_call_requests.call_context.contract_address]
      inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

      [app_public_inputs.private_call_requests.call_context.function_selector]
      inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.private_call_requests]]
  args_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"
  returns_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"
  start_side_effect_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
  end_side_effect_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [app_public_inputs.private_call_requests.call_context]
    is_static_call = false

      [app_public_inputs.private_call_requests.call_context.msg_sender]
      inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

      [app_public_inputs.private_call_requests.call_context.contract_address]
      inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

      [app_public_inputs.private_call_requests.call_context.function_selector]
      inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.private_call_requests]]
  args_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"
  returns_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"
  start_side_effect_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
  end_side_effect_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [app_public_inputs.private_call_requests.call_context]
    is_static_call = false

      [app_public_inputs.private_call_requests.call_context.msg_sender]
      inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

      [app_public_inputs.private_call_requests.call_context.contract_address]
      inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

      [app_public_inputs.private_call_requests.call_context.function_selector]
      inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.private_call_requests]]
  args_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"
  returns_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"
  start_side_effect_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
  end_side_effect_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [app_public_inputs.private_call_requests.call_context]
    is_static_call = false

      [app_public_inputs.private_call_requests.call_context.msg_sender]
      inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

      [app_public_inputs.private_call_requests.call_context.contract_address]
      inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

      [app_public_inputs.private_call_requests.call_context.function_selector]
      inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.public_call_requests]]
  counter = "0x0000000000000000000000000000000000000000000000000000000000000003"

    [app_public_inputs.public_call_requests.inner]
    is_static_call = true
    calldata_hash = "0x0753b92cfce6df3af08d88c8bad668e96021a774ac6adb79775e0bd5b828146d"

      [app_public_inputs.public_call_requests.inner.msg_sender]
      inner = "0x2bd6622f715bd307d4918e462bd9aa829794c19bfe8ebbe5390237361e65e437"

      [app_public_inputs.public_call_requests.inner.contract_address]
      inner = "0x2aa83d8a54e3931f79d1923248fa2aafa441fbd0e8df1989f1fb70a910a49471"

  [[app_public_inputs.public_call_requests]]
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [app_public_inputs.public_call_requests.inner]
    is_static_call = false
    calldata_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

      [app_public_inputs.public_call_requests.inner.msg_sender]
      inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

      [app_public_inputs.public_call_requests.inner.contract_address]
      inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.public_call_requests]]
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [app_public_inputs.public_call_requests.inner]
    is_static_call = false
    calldata_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

      [app_public_inputs.public_call_requests.inner.msg_sender]
      inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

      [app_public_inputs.public_call_requests.inner.contract_address]
      inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.public_call_requests]]
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [app_public_inputs.public_call_requests.inner]
    is_static_call = false
    calldata_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

      [app_public_inputs.public_call_requests.inner.msg_sender]
      inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

      [app_public_inputs.public_call_requests.inner.contract_address]
      inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.public_call_requests]]
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [app_public_inputs.public_call_requests.inner]
    is_static_call = false
    calldata_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

      [app_public_inputs.public_call_requests.inner.msg_sender]
      inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

      [app_public_inputs.public_call_requests.inner.contract_address]
      inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.public_call_requests]]
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [app_public_inputs.public_call_requests.inner]
    is_static_call = false
    calldata_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

      [app_public_inputs.public_call_requests.inner.msg_sender]
      inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

      [app_public_inputs.public_call_requests.inner.contract_address]
      inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.public_call_requests]]
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [app_public_inputs.public_call_requests.inner]
    is_static_call = false
    calldata_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

      [app_public_inputs.public_call_requests.inner.msg_sender]
      inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

      [app_public_inputs.public_call_requests.inner.contract_address]
      inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.public_call_requests]]
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [app_public_inputs.public_call_requests.inner]
    is_static_call = false
    calldata_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

      [app_public_inputs.public_call_requests.inner.msg_sender]
      inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

      [app_public_inputs.public_call_requests.inner.contract_address]
      inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.public_call_requests]]
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [app_public_inputs.public_call_requests.inner]
    is_static_call = false
    calldata_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

      [app_public_inputs.public_call_requests.inner.msg_sender]
      inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

      [app_public_inputs.public_call_requests.inner.contract_address]
      inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.public_call_requests]]
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [app_public_inputs.public_call_requests.inner]
    is_static_call = false
    calldata_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

      [app_public_inputs.public_call_requests.inner.msg_sender]
      inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

      [app_public_inputs.public_call_requests.inner.contract_address]
      inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.public_call_requests]]
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [app_public_inputs.public_call_requests.inner]
    is_static_call = false
    calldata_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

      [app_public_inputs.public_call_requests.inner.msg_sender]
      inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

      [app_public_inputs.public_call_requests.inner.contract_address]
      inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.public_call_requests]]
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [app_public_inputs.public_call_requests.inner]
    is_static_call = false
    calldata_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

      [app_public_inputs.public_call_requests.inner.msg_sender]
      inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

      [app_public_inputs.public_call_requests.inner.contract_address]
      inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.public_call_requests]]
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [app_public_inputs.public_call_requests.inner]
    is_static_call = false
    calldata_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

      [app_public_inputs.public_call_requests.inner.msg_sender]
      inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

      [app_public_inputs.public_call_requests.inner.contract_address]
      inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.public_call_requests]]
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [app_public_inputs.public_call_requests.inner]
    is_static_call = false
    calldata_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

      [app_public_inputs.public_call_requests.inner.msg_sender]
      inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

      [app_public_inputs.public_call_requests.inner.contract_address]
      inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.public_call_requests]]
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [app_public_inputs.public_call_requests.inner]
    is_static_call = false
    calldata_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

      [app_public_inputs.public_call_requests.inner.msg_sender]
      inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

      [app_public_inputs.public_call_requests.inner.contract_address]
      inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.public_call_requests]]
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [app_public_inputs.public_call_requests.inner]
    is_static_call = false
    calldata_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

      [app_public_inputs.public_call_requests.inner.msg_sender]
      inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

      [app_public_inputs.public_call_requests.inner.contract_address]
      inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [app_public_inputs.public_teardown_call_request]
  is_static_call = false
  calldata_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [app_public_inputs.public_teardown_call_request.msg_sender]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [app_public_inputs.public_teardown_call_request.contract_address]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.l2_to_l1_msgs]]
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [app_public_inputs.l2_to_l1_msgs.inner]
    content = "0x0000000000000000000000000000000000000000000000000000000000000000"

      [app_public_inputs.l2_to_l1_msgs.inner.recipient]
      inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.l2_to_l1_msgs]]
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [app_public_inputs.l2_to_l1_msgs.inner]
    content = "0x0000000000000000000000000000000000000000000000000000000000000000"

      [app_public_inputs.l2_to_l1_msgs.inner.recipient]
      inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.private_logs]]
  note_hash_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [app_public_inputs.private_logs.log]
    fields = [
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000"
]
    length = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.private_logs]]
  note_hash_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [app_public_inputs.private_logs.log]
    fields = [
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000"
]
    length = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.private_logs]]
  note_hash_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [app_public_inputs.private_logs.log]
    fields = [
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000"
]
    length = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.private_logs]]
  note_hash_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [app_public_inputs.private_logs.log]
    fields = [
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000"
]
    length = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.private_logs]]
  note_hash_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [app_public_inputs.private_logs.log]
    fields = [
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000"
]
    length = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.private_logs]]
  note_hash_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [app_public_inputs.private_logs.log]
    fields = [
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000"
]
    length = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.private_logs]]
  note_hash_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [app_public_inputs.private_logs.log]
    fields = [
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000"
]
    length = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.private_logs]]
  note_hash_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [app_public_inputs.private_logs.log]
    fields = [
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000"
]
    length = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.private_logs]]
  note_hash_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [app_public_inputs.private_logs.log]
    fields = [
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000"
]
    length = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.private_logs]]
  note_hash_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [app_public_inputs.private_logs.log]
    fields = [
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000"
]
    length = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.private_logs]]
  note_hash_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [app_public_inputs.private_logs.log]
    fields = [
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000"
]
    length = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.private_logs]]
  note_hash_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [app_public_inputs.private_logs.log]
    fields = [
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000"
]
    length = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.private_logs]]
  note_hash_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [app_public_inputs.private_logs.log]
    fields = [
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000"
]
    length = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.private_logs]]
  note_hash_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [app_public_inputs.private_logs.log]
    fields = [
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000"
]
    length = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.private_logs]]
  note_hash_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [app_public_inputs.private_logs.log]
    fields = [
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000"
]
    length = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.private_logs]]
  note_hash_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [app_public_inputs.private_logs.log]
    fields = [
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000"
]
    length = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [[app_public_inputs.contract_class_logs_hashes]]
  counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [app_public_inputs.contract_class_logs_hashes.inner]
    value = "0x0000000000000000000000000000000000000000000000000000000000000000"
    length = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [app_public_inputs.historical_header]
  total_fees = "0x00000000000000000000000000000000000000000000000000000000741958c4"
  total_mana_used = "0x000000000000000000000000000000000000000000000000000000000000d23e"

    [app_public_inputs.historical_header.last_archive]
    root = "0x07d03443b8719aac8b4b54ab132940fc21800149845bd28697ed3baeaa21a523"
    next_available_leaf_index = "0x0000000000000000000000000000000000000000000000000000000000000006"

    [app_public_inputs.historical_header.content_commitment]
    blobs_hash = "0x000c0a16f870bff67605e8c18de39068658cbafa5444fff90752364563815ec6"
    in_hash = "0x00089a9d421a82c4a25f7acbebe69e638d5b064fa8a60e018793dcb0be53752c"
    out_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

[app_public_inputs.historical_header.state.l1_to_l2_message_tree]
root = "0x2e33ee2008411c04b99c24b313513d097a0d21a5040b6193d1f978b8226892d6"
next_available_leaf_index = "0x0000000000000000000000000000000000000000000000000000000000000060"

[app_public_inputs.historical_header.state.partial.note_hash_tree]
root = "0x10c1f741a0fa706e1eddb3bf23cb6f1305bfb60d621af66f5427d0d67d0b312a"
next_available_leaf_index = "0x0000000000000000000000000000000000000000000000000000000000000180"

[app_public_inputs.historical_header.state.partial.nullifier_tree]
root = "0x0e4db848d4729ffb24a540d50688a3db38e38460da4eadd9a13308b2405ec6d9"
next_available_leaf_index = "0x0000000000000000000000000000000000000000000000000000000000000200"

[app_public_inputs.historical_header.state.partial.public_data_tree]
root = "0x1fcc6dea40abf7522d330bc6c1d187fc476f4a3c3af9f16d9dc2c4c0cb8ff092"
next_available_leaf_index = "0x000000000000000000000000000000000000000000000000000000000000008a"

    [app_public_inputs.historical_header.global_variables]
    chain_id = "0x0000000000000000000000000000000000000000000000000000000000007a69"
    version = "0x0000000000000000000000000000000000000000000000000000000055c58b0d"
    block_number = "0x0000000000000000000000000000000000000000000000000000000000000006"
    slot_number = "0x0000000000000000000000000000000000000000000000000000000000000007"
    timestamp = "0x00000000000000000000000000000000000000000000000000000000684882f2"

      [app_public_inputs.historical_header.global_variables.coinbase]
      inner = "0x0000000000000000000000008e7508851cd7c32bc45138c6315abb45e66adbbb"

      [app_public_inputs.historical_header.global_variables.fee_recipient]
      inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

      [app_public_inputs.historical_header.global_variables.gas_fees]
      fee_per_da_gas = "0x0000000000000000000000000000000000000000000000000000000000000000"
      fee_per_l2_gas = "0x0000000000000000000000000000000000000000000000000000000000008d5e"

  [app_public_inputs.tx_context]
  chain_id = "0x0000000000000000000000000000000000000000000000000000000000007a69"
  version = "0x0000000000000000000000000000000000000000000000000000000055c58b0d"

[app_public_inputs.tx_context.gas_settings.gas_limits]
da_gas = "0x000000000000000000000000000000000000000000000000000000003b9aca00"
l2_gas = "0x000000000000000000000000000000000000000000000000000000003b9aca00"

[app_public_inputs.tx_context.gas_settings.teardown_gas_limits]
da_gas = "0x00000000000000000000000000000000000000000000000000000000005b8d80"
l2_gas = "0x00000000000000000000000000000000000000000000000000000000005b8d80"

[app_public_inputs.tx_context.gas_settings.max_fees_per_gas]
fee_per_da_gas = "0x0000000000000000000000000000000000000000000000000000000000000000"
fee_per_l2_gas = "0x000000000000000000000000000000000000000000000000000000000000d40d"

[app_public_inputs.tx_context.gas_settings.max_priority_fees_per_gas]
fee_per_da_gas = "0x0000000000000000000000000000000000000000000000000000000000000000"
fee_per_l2_gas = "0x0000000000000000000000000000000000000000000000000000000000000000"
