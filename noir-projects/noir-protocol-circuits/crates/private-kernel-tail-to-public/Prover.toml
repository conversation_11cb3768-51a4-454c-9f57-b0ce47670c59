[previous_kernel.vk_data]
leaf_index = "0x000000000000000000000000000000000000000000000000000000000000003a"
sibling_path = [
  "0x1076fa178aa2a95533225cb0f21a78e48dc3da29710fb6a549e8f9d078c32631",
  "0x122c4e49c354f8f2e65723ac49d102b4b76f3fd9319eeff139457d2543a0573a",
  "0x0425d06d76bd5608469887547fced7b6a35b4a8e17b2815e8c99620de05b2d34",
  "0x02c727fb5327ef2f881a2ad2fc0a93ac1b5d23a401b07ac42d405c7f04fc4d14",
  "0x20fa1410abac15224fc8d94e186e86174395ec2bf4ddb6a87fac41d755a8b25e",
  "0x2d8d626fbe7b670265cf17ab7b750297e5711f54292ab944c1a807dec661dafb"
]

  [previous_kernel.vk_data.vk]
  key = [
  "0x0000000000000000000000000000000000000000000000000000000000040000",
  "0x0000000000000000000000000000000000000000000000000000000000000020",
  "0x0000000000000000000000000000000000000000000000000000000000005609",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000018",
  "0x0000000000000000000000000000000000000000000000000000000000000010",
  "0x0000000000000000000000000000000000000000000000000000000000000001",
  "0x000000000000000000000000000000f6fdcd868e9f27cd009fe2306aa756b78b",
  "0x000000000000000000000000000000000019f2a241aaf17b8dfc1d2d69e74373",
  "0x0000000000000000000000000000005276883421115c6eb5609a74a220acfb19",
  "0x00000000000000000000000000000000000041ecfecd460f20db037bf52a1a4b",
  "0x000000000000000000000000000000024c22c5462b40745cb757e783ed3428e7",
  "0x00000000000000000000000000000000001f69c1a3248659a2f3169011f4fd97",
  "0x000000000000000000000000000000f547925249d3f899d976b7cc15bf46ec84",
  "0x00000000000000000000000000000000001d8a8eefdd8256cf53c61b3fb2cf4b",
  "0x000000000000000000000000000000f3ae88ac092af3fd666f7f18556bd0829a",
  "0x0000000000000000000000000000000000099e0f2414ee75155157c49fea662c",
  "0x00000000000000000000000000000061c519d91485ad642d841d6a19cc5e8b38",
  "0x00000000000000000000000000000000000cadfdfb7142105b3680c31f5d851a",
  "0x0000000000000000000000000000006726ec0a97bb6febc2517162d8efa3297f",
  "0x000000000000000000000000000000000016d75523e0769d5299f8a190f2e950",
  "0x000000000000000000000000000000d7ecb9448157bfb771a0108e11fa65ac27",
  "0x0000000000000000000000000000000000251bfc7b06a82f5cb23d84a4932ea6",
  "0x0000000000000000000000000000007ed4cc5d8723b4c97dfe8501621aeb8796",
  "0x000000000000000000000000000000000007d8e306df6678d978fec829f02770",
  "0x00000000000000000000000000000026859e3384a6ce238327182670ef731eff",
  "0x00000000000000000000000000000000001a74c2488797dd3374d47b2fd57ecd",
  "0x0000000000000000000000000000004e5a0e3f1f8f22a7fd44a7322932a9c826",
  "0x000000000000000000000000000000000015dd9e4519f151cc1b9e99a0a19ec0",
  "0x000000000000000000000000000000d243214c3c1ac45e946830b789432bb510",
  "0x0000000000000000000000000000000000103b9c58f93379240add0cbf0d83f6",
  "0x0000000000000000000000000000003be0f2fa355b9fc17357613b21a51c9866",
  "0x000000000000000000000000000000000007c5124717cc8c60ff46251d6b4bc5",
  "0x0000000000000000000000000000007166fb7ac2b5fb8d2975ef0a0247259812",
  "0x000000000000000000000000000000000029108df813a3808ef541708c4a7e81",
  "0x00000000000000000000000000000090d53c6a3b26339cda6b73df9208314159",
  "0x0000000000000000000000000000000000298c3311fc9170f92de940b042aab9",
  "0x000000000000000000000000000000bf37537eb196b05c1d98fa51016a9bacbb",
  "0x000000000000000000000000000000000007b05f408a612847259016b9204ae4",
  "0x0000000000000000000000000000000735495f7a61888a386dc44873443707d9",
  "0x000000000000000000000000000000000009ba2a3f33a159bb90771ca7b59a8d",
  "0x000000000000000000000000000000a9948374de96de3c0a9bc7d0b0ab34b4fe",
  "0x00000000000000000000000000000000000ec4cfb32cc64d7a61b541152dae4b",
  "0x00000000000000000000000000000077ad8eb1f0b40e5f99e758d12c46e73219",
  "0x00000000000000000000000000000000000d5e58bd6acdaac1b4e121fb2c9c06",
  "0x00000000000000000000000000000017d29de207e79e03c59694322c2447b03e",
  "0x000000000000000000000000000000000020d81405ba7c007ae4b6e4976f50ea",
  "0x000000000000000000000000000000a7750b15aa5acd381615a421c753087d57",
  "0x00000000000000000000000000000000002369330f20f148d689a9ea087a939e",
  "0x0000000000000000000000000000004e15a347b3dfff385b4cc18f28147d9a7d",
  "0x00000000000000000000000000000000000c2349683fa4bd76d8caaba23e1b5a",
  "0x0000000000000000000000000000005cbcc8b84da7d140f8344dbc74237b416d",
  "0x0000000000000000000000000000000000073e736acf5a580f2eae111777dca8",
  "0x0000000000000000000000000000005ac57f35ac3c804f7e41d992d56f523ad6",
  "0x00000000000000000000000000000000002da802a43cefaa2509311581d721fb",
  "0x00000000000000000000000000000049e1d87b64b6d0037ad198536b2db2cc18",
  "0x00000000000000000000000000000000001e980c9636756cf4f47e9905c760af",
  "0x000000000000000000000000000000a45bbb6288037050320808bc5b4d8331a1",
  "0x00000000000000000000000000000000000ab61fb9cb6f35ed08ef54d645b955",
  "0x000000000000000000000000000000b53cc56fcf1c5e58c93d94f087d1c97b2b",
  "0x000000000000000000000000000000000002d39bfdc5e431e9e9495c252b49c2",
  "0x00000000000000000000000000000022b2bffa3c596bfc75c3f231ba3c1e5223",
  "0x0000000000000000000000000000000000279448ce0ed1d4061a24e98d13a94c",
  "0x0000000000000000000000000000008b042e353fb006cabe8184edcb4b7189c0",
  "0x000000000000000000000000000000000022034084abf28cdcc1bfc9a181f397",
  "0x0000000000000000000000000000001f764f781e1a4ed216c57e575af48cd474",
  "0x00000000000000000000000000000000000bf7f3ca7dee230d6ac99e6d2c6e65",
  "0x000000000000000000000000000000a04c246a7b45c675de955e28ae3ee39f08",
  "0x0000000000000000000000000000000000072dcd099b91c6e4efd901a892b606",
  "0x0000000000000000000000000000002803504c9cd77773c73c6de9d6c91c04d3",
  "0x000000000000000000000000000000000024db8254d407a191fe613840df75cc",
  "0x0000000000000000000000000000006e803aa98829f98fed2b613d099ef03dc2",
  "0x000000000000000000000000000000000016a2732a64a302efd755030a69c6f8",
  "0x0000000000000000000000000000006f8204de74499172bb78c456de981c64bc",
  "0x000000000000000000000000000000000024b325758e031c4f8873c379986876",
  "0x00000000000000000000000000000065378c61b7cabe49adca865e52cb27b231",
  "0x00000000000000000000000000000000000cb82ddaa7d91f05de39803cad914d",
  "0x000000000000000000000000000000264141520f0a316065b62fbdd9292ce0f4",
  "0x00000000000000000000000000000000000290d218c467ea6b53250232baf5f6",
  "0x000000000000000000000000000000d132b994e96db4e56e499b0290436b889e",
  "0x000000000000000000000000000000000004965c781800c5996f59dbdf6cc299",
  "0x0000000000000000000000000000009e6dd1d79e8e84e97b677f3aa84259343f",
  "0x000000000000000000000000000000000010d66f2a288fa400c9fa42b30e917d",
  "0x000000000000000000000000000000d89f7fde202e4817ce7d0be86d99081b4b",
  "0x00000000000000000000000000000000001f76c29aef87ad0ee05c1c8feaf11a",
  "0x0000000000000000000000000000006f9dceae1b75ba7ba05aa6b46195853614",
  "0x000000000000000000000000000000000021e0850e0162ed1251b264a394546e",
  "0x000000000000000000000000000000a6078d48c333dfe02acf15491a4c02d8eb",
  "0x0000000000000000000000000000000000093489461c6ecda93a40f078b89087",
  "0x00000000000000000000000000000088a3dd6b89efe93fdc46b6e6c9dcd632ae",
  "0x00000000000000000000000000000000000e7567c6e87ae2903f13119324dc61",
  "0x0000000000000000000000000000002ee219349e733ac4a864999f7bd44317f6",
  "0x00000000000000000000000000000000002fc80debaf72a4dd8aca424538584a",
  "0x0000000000000000000000000000005246ae2e9e8d1836e36ab9bf3ec999a429",
  "0x0000000000000000000000000000000000012c1b047188b4538d7084d0d1e16d",
  "0x000000000000000000000000000000f6f4596202301b6ae4eb0ebbeadd203340",
  "0x00000000000000000000000000000000000adc89c48d75b571636f5bbeb4a806",
  "0x00000000000000000000000000000000034e3e27454ef992b4bf84b97baa7471",
  "0x0000000000000000000000000000000000066f28135748f119631c3fe07fa9d7",
  "0x0000000000000000000000000000003b64a66f2ac4979b65e56568c5a31b14ed",
  "0x00000000000000000000000000000000002e25783551df50c004ec7cd1f4dd8b",
  "0x000000000000000000000000000000e8258f84477c1b62565a559ba7bb38832e",
  "0x000000000000000000000000000000000018f76cf0ceeccb4798de741ae89b64",
  "0x000000000000000000000000000000353d43fa70e99239c1c1c67e271a0eeac5",
  "0x00000000000000000000000000000000002d299fb68678d0150bcc5b16dc8252",
  "0x0000000000000000000000000000002814ede7cd27daed00c33c12860bc4b046",
  "0x000000000000000000000000000000000015d3ac5a199abb74933a4efc98c59b",
  "0x000000000000000000000000000000469680c270e551515344592f59188fa765",
  "0x00000000000000000000000000000000002d38d6d4ba1e4763a74ecdb11ca1f3",
  "0x000000000000000000000000000000fce917c0d5dca019477c52f6075332b612",
  "0x000000000000000000000000000000000012db39e892826b32610ee08251e005",
  "0x0000000000000000000000000000000000000000000000000000000000000001",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000002",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000c2f844425e4883152281bda6449a1c52f",
  "0x00000000000000000000000000000000001c1719fa7ec2b19fda054cd775796e",
  "0x0000000000000000000000000000009d91e49715bec8e0ce20afe94212b07326",
  "0x00000000000000000000000000000000001475a523608f68bb602572f813d103",
  "0x000000000000000000000000000000aff461b2b09a0b7a7c58838cc0f51f541f",
  "0x0000000000000000000000000000000000294422a7e7114973a579b85bac8c02",
  "0x0000000000000000000000000000009a5fee2dfe718a5ea270861c5e307dbc5f",
  "0x000000000000000000000000000000000006d083ed8cd79ec5c0ed21eee50e54",
  "0x000000000000000000000000000000e55ba19751adfe6c36324d3fb6c2da0989",
  "0x00000000000000000000000000000000001d58aa61c64ad522043d79c4802219",
  "0x00000000000000000000000000000078f4b3bc61f19d6e7069359bbf47e7f907",
  "0x00000000000000000000000000000000002d7c18a93c3dae58809faaeec6a86a"
]
  hash = "0x2754c7421cf219b9578f9994067fecf903eade76e2a3fe62a78681c33b6d6d19"

[previous_kernel_public_inputs]
min_revertible_side_effect_counter = "0x0000000000000000000000000000000000000000000000000000000000000003"
is_private_only = false
claimed_first_nullifier = "0x145c69e0317226be0054019c8f00cdd1f74fe1a781654f4954f7211c559b896b"

  [previous_kernel_public_inputs.constants]
  vk_tree_root = "0x1ee6a40f9c295dbdbcaf4eafcba42c488330caff248419f6a0100ed9d5b2719c"
  protocol_contract_tree_root = "0x0dbfba3eddc8d0560547d65dac94beb59c45bb56de9df596e338505b620fa335"

    [previous_kernel_public_inputs.constants.historical_header]
    total_fees = "0x00000000000000000000000000000000000000000000000000000000741958c4"
    total_mana_used = "0x000000000000000000000000000000000000000000000000000000000000d23e"

      [previous_kernel_public_inputs.constants.historical_header.last_archive]
      root = "0x07d03443b8719aac8b4b54ab132940fc21800149845bd28697ed3baeaa21a523"
      next_available_leaf_index = "0x0000000000000000000000000000000000000000000000000000000000000006"

      [previous_kernel_public_inputs.constants.historical_header.content_commitment]
      blobs_hash = "0x000c0a16f870bff67605e8c18de39068658cbafa5444fff90752364563815ec6"
      in_hash = "0x00089a9d421a82c4a25f7acbebe69e638d5b064fa8a60e018793dcb0be53752c"
      out_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.constants.historical_header.state.l1_to_l2_message_tree]
root = "0x2e33ee2008411c04b99c24b313513d097a0d21a5040b6193d1f978b8226892d6"
next_available_leaf_index = "0x0000000000000000000000000000000000000000000000000000000000000060"

[previous_kernel_public_inputs.constants.historical_header.state.partial.note_hash_tree]
root = "0x10c1f741a0fa706e1eddb3bf23cb6f1305bfb60d621af66f5427d0d67d0b312a"
next_available_leaf_index = "0x0000000000000000000000000000000000000000000000000000000000000180"

[previous_kernel_public_inputs.constants.historical_header.state.partial.nullifier_tree]
root = "0x0e4db848d4729ffb24a540d50688a3db38e38460da4eadd9a13308b2405ec6d9"
next_available_leaf_index = "0x0000000000000000000000000000000000000000000000000000000000000200"

[previous_kernel_public_inputs.constants.historical_header.state.partial.public_data_tree]
root = "0x1fcc6dea40abf7522d330bc6c1d187fc476f4a3c3af9f16d9dc2c4c0cb8ff092"
next_available_leaf_index = "0x000000000000000000000000000000000000000000000000000000000000008a"

      [previous_kernel_public_inputs.constants.historical_header.global_variables]
      chain_id = "0x0000000000000000000000000000000000000000000000000000000000007a69"
      version = "0x0000000000000000000000000000000000000000000000000000000055c58b0d"
      block_number = "0x0000000000000000000000000000000000000000000000000000000000000006"
      slot_number = "0x0000000000000000000000000000000000000000000000000000000000000007"
      timestamp = "0x00000000000000000000000000000000000000000000000000000000684882f2"

        [previous_kernel_public_inputs.constants.historical_header.global_variables.coinbase]
        inner = "0x0000000000000000000000008e7508851cd7c32bc45138c6315abb45e66adbbb"

        [previous_kernel_public_inputs.constants.historical_header.global_variables.fee_recipient]
        inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

        [previous_kernel_public_inputs.constants.historical_header.global_variables.gas_fees]
        fee_per_da_gas = "0x0000000000000000000000000000000000000000000000000000000000000000"
        fee_per_l2_gas = "0x0000000000000000000000000000000000000000000000000000000000008d5e"

    [previous_kernel_public_inputs.constants.tx_context]
    chain_id = "0x0000000000000000000000000000000000000000000000000000000000007a69"
    version = "0x0000000000000000000000000000000000000000000000000000000055c58b0d"

[previous_kernel_public_inputs.constants.tx_context.gas_settings.gas_limits]
da_gas = "0x000000000000000000000000000000000000000000000000000000003b9aca00"
l2_gas = "0x000000000000000000000000000000000000000000000000000000003b9aca00"

[previous_kernel_public_inputs.constants.tx_context.gas_settings.teardown_gas_limits]
da_gas = "0x00000000000000000000000000000000000000000000000000000000005b8d80"
l2_gas = "0x00000000000000000000000000000000000000000000000000000000005b8d80"

[previous_kernel_public_inputs.constants.tx_context.gas_settings.max_fees_per_gas]
fee_per_da_gas = "0x0000000000000000000000000000000000000000000000000000000000000000"
fee_per_l2_gas = "0x000000000000000000000000000000000000000000000000000000000000d40d"

[previous_kernel_public_inputs.constants.tx_context.gas_settings.max_priority_fees_per_gas]
fee_per_da_gas = "0x0000000000000000000000000000000000000000000000000000000000000000"
fee_per_l2_gas = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.for_rollup.max_block_number._opt]
_is_some = true
_value = "0x0000000000000000000000000000000000000000000000000000000000000e16"

[[previous_kernel_public_inputs.validation_requests.note_hash_read_requests]]
[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.note_hash_read_requests]]
[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.note_hash_read_requests]]
[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.note_hash_read_requests]]
[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.note_hash_read_requests]]
[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.note_hash_read_requests]]
[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.note_hash_read_requests]]
[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.note_hash_read_requests]]
[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.note_hash_read_requests]]
[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.note_hash_read_requests]]
[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.note_hash_read_requests]]
[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.note_hash_read_requests]]
[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.note_hash_read_requests]]
[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.note_hash_read_requests]]
[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.note_hash_read_requests]]
[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.note_hash_read_requests]]
[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.note_hash_read_requests]]
[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.note_hash_read_requests]]
[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.note_hash_read_requests]]
[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.note_hash_read_requests]]
[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.note_hash_read_requests]]
[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.note_hash_read_requests]]
[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.note_hash_read_requests]]
[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.note_hash_read_requests]]
[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.note_hash_read_requests]]
[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.note_hash_read_requests]]
[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.note_hash_read_requests]]
[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.note_hash_read_requests]]
[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.note_hash_read_requests]]
[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.note_hash_read_requests]]
[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.note_hash_read_requests]]
[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.note_hash_read_requests]]
[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.note_hash_read_requests]]
[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.note_hash_read_requests]]
[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.note_hash_read_requests]]
[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.note_hash_read_requests]]
[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.note_hash_read_requests]]
[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.note_hash_read_requests]]
[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.note_hash_read_requests]]
[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.note_hash_read_requests]]
[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.note_hash_read_requests]]
[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.note_hash_read_requests]]
[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.note_hash_read_requests]]
[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.note_hash_read_requests]]
[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.note_hash_read_requests]]
[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.note_hash_read_requests]]
[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.note_hash_read_requests]]
[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.note_hash_read_requests]]
[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.note_hash_read_requests]]
[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.note_hash_read_requests]]
[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.note_hash_read_requests]]
[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.note_hash_read_requests]]
[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.note_hash_read_requests]]
[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.note_hash_read_requests]]
[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.note_hash_read_requests]]
[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.note_hash_read_requests]]
[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.note_hash_read_requests]]
[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.note_hash_read_requests]]
[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.note_hash_read_requests]]
[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.note_hash_read_requests]]
[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.note_hash_read_requests]]
[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.note_hash_read_requests]]
[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.note_hash_read_requests]]
[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.note_hash_read_requests]]
[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.note_hash_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.nullifier_read_requests]]
[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.nullifier_read_requests]]
[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.nullifier_read_requests]]
[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.nullifier_read_requests]]
[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.nullifier_read_requests]]
[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.nullifier_read_requests]]
[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.nullifier_read_requests]]
[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.nullifier_read_requests]]
[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.nullifier_read_requests]]
[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.nullifier_read_requests]]
[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.nullifier_read_requests]]
[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.nullifier_read_requests]]
[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.nullifier_read_requests]]
[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.nullifier_read_requests]]
[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.nullifier_read_requests]]
[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.nullifier_read_requests]]
[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.nullifier_read_requests]]
[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.nullifier_read_requests]]
[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.nullifier_read_requests]]
[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.nullifier_read_requests]]
[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.nullifier_read_requests]]
[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.nullifier_read_requests]]
[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.nullifier_read_requests]]
[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.nullifier_read_requests]]
[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.nullifier_read_requests]]
[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.nullifier_read_requests]]
[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.nullifier_read_requests]]
[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.nullifier_read_requests]]
[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.nullifier_read_requests]]
[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.nullifier_read_requests]]
[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.nullifier_read_requests]]
[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.nullifier_read_requests]]
[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.nullifier_read_requests]]
[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.nullifier_read_requests]]
[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.nullifier_read_requests]]
[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.nullifier_read_requests]]
[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.nullifier_read_requests]]
[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.nullifier_read_requests]]
[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.nullifier_read_requests]]
[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.nullifier_read_requests]]
[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.nullifier_read_requests]]
[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.nullifier_read_requests]]
[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.nullifier_read_requests]]
[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.nullifier_read_requests]]
[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.nullifier_read_requests]]
[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.nullifier_read_requests]]
[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.nullifier_read_requests]]
[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.nullifier_read_requests]]
[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.nullifier_read_requests]]
[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.nullifier_read_requests]]
[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.nullifier_read_requests]]
[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.nullifier_read_requests]]
[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.nullifier_read_requests]]
[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.nullifier_read_requests]]
[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.nullifier_read_requests]]
[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.nullifier_read_requests]]
[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.nullifier_read_requests]]
[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.nullifier_read_requests]]
[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.nullifier_read_requests]]
[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.nullifier_read_requests]]
[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.nullifier_read_requests]]
[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.nullifier_read_requests]]
[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.nullifier_read_requests]]
[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.nullifier_read_requests]]
[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.read_request]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.nullifier_read_requests.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators]]
[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request]
sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request]
  sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request.pk_m]
    x = "0x0000000000000000000000000000000000000000000000000000000000000000"
    y = "0x0000000000000000000000000000000000000000000000000000000000000000"
    is_infinite = false

[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators]]
[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request]
sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request]
  sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request.pk_m]
    x = "0x0000000000000000000000000000000000000000000000000000000000000000"
    y = "0x0000000000000000000000000000000000000000000000000000000000000000"
    is_infinite = false

[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators]]
[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request]
sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request]
  sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request.pk_m]
    x = "0x0000000000000000000000000000000000000000000000000000000000000000"
    y = "0x0000000000000000000000000000000000000000000000000000000000000000"
    is_infinite = false

[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators]]
[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request]
sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request]
  sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request.pk_m]
    x = "0x0000000000000000000000000000000000000000000000000000000000000000"
    y = "0x0000000000000000000000000000000000000000000000000000000000000000"
    is_infinite = false

[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators]]
[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request]
sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request]
  sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request.pk_m]
    x = "0x0000000000000000000000000000000000000000000000000000000000000000"
    y = "0x0000000000000000000000000000000000000000000000000000000000000000"
    is_infinite = false

[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators]]
[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request]
sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request]
  sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request.pk_m]
    x = "0x0000000000000000000000000000000000000000000000000000000000000000"
    y = "0x0000000000000000000000000000000000000000000000000000000000000000"
    is_infinite = false

[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators]]
[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request]
sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request]
  sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request.pk_m]
    x = "0x0000000000000000000000000000000000000000000000000000000000000000"
    y = "0x0000000000000000000000000000000000000000000000000000000000000000"
    is_infinite = false

[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators]]
[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request]
sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request]
  sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request.pk_m]
    x = "0x0000000000000000000000000000000000000000000000000000000000000000"
    y = "0x0000000000000000000000000000000000000000000000000000000000000000"
    is_infinite = false

[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators]]
[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request]
sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request]
  sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request.pk_m]
    x = "0x0000000000000000000000000000000000000000000000000000000000000000"
    y = "0x0000000000000000000000000000000000000000000000000000000000000000"
    is_infinite = false

[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators]]
[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request]
sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request]
  sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request.pk_m]
    x = "0x0000000000000000000000000000000000000000000000000000000000000000"
    y = "0x0000000000000000000000000000000000000000000000000000000000000000"
    is_infinite = false

[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators]]
[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request]
sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request]
  sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request.pk_m]
    x = "0x0000000000000000000000000000000000000000000000000000000000000000"
    y = "0x0000000000000000000000000000000000000000000000000000000000000000"
    is_infinite = false

[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators]]
[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request]
sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request]
  sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request.pk_m]
    x = "0x0000000000000000000000000000000000000000000000000000000000000000"
    y = "0x0000000000000000000000000000000000000000000000000000000000000000"
    is_infinite = false

[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators]]
[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request]
sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request]
  sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request.pk_m]
    x = "0x0000000000000000000000000000000000000000000000000000000000000000"
    y = "0x0000000000000000000000000000000000000000000000000000000000000000"
    is_infinite = false

[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators]]
[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request]
sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request]
  sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request.pk_m]
    x = "0x0000000000000000000000000000000000000000000000000000000000000000"
    y = "0x0000000000000000000000000000000000000000000000000000000000000000"
    is_infinite = false

[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators]]
[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request]
sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request]
  sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request.pk_m]
    x = "0x0000000000000000000000000000000000000000000000000000000000000000"
    y = "0x0000000000000000000000000000000000000000000000000000000000000000"
    is_infinite = false

[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators]]
[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request]
sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request]
  sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request.pk_m]
    x = "0x0000000000000000000000000000000000000000000000000000000000000000"
    y = "0x0000000000000000000000000000000000000000000000000000000000000000"
    is_infinite = false

[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators]]
[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request]
sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request]
  sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request.pk_m]
    x = "0x0000000000000000000000000000000000000000000000000000000000000000"
    y = "0x0000000000000000000000000000000000000000000000000000000000000000"
    is_infinite = false

[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators]]
[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request]
sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request]
  sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request.pk_m]
    x = "0x0000000000000000000000000000000000000000000000000000000000000000"
    y = "0x0000000000000000000000000000000000000000000000000000000000000000"
    is_infinite = false

[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators]]
[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request]
sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request]
  sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request.pk_m]
    x = "0x0000000000000000000000000000000000000000000000000000000000000000"
    y = "0x0000000000000000000000000000000000000000000000000000000000000000"
    is_infinite = false

[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators]]
[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request]
sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request]
  sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request.pk_m]
    x = "0x0000000000000000000000000000000000000000000000000000000000000000"
    y = "0x0000000000000000000000000000000000000000000000000000000000000000"
    is_infinite = false

[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators]]
[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request]
sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request]
  sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request.pk_m]
    x = "0x0000000000000000000000000000000000000000000000000000000000000000"
    y = "0x0000000000000000000000000000000000000000000000000000000000000000"
    is_infinite = false

[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators]]
[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request]
sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request]
  sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request.pk_m]
    x = "0x0000000000000000000000000000000000000000000000000000000000000000"
    y = "0x0000000000000000000000000000000000000000000000000000000000000000"
    is_infinite = false

[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators]]
[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request]
sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request]
  sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request.pk_m]
    x = "0x0000000000000000000000000000000000000000000000000000000000000000"
    y = "0x0000000000000000000000000000000000000000000000000000000000000000"
    is_infinite = false

[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators]]
[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request]
sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request]
  sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request.pk_m]
    x = "0x0000000000000000000000000000000000000000000000000000000000000000"
    y = "0x0000000000000000000000000000000000000000000000000000000000000000"
    is_infinite = false

[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators]]
[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request]
sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request]
  sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request.pk_m]
    x = "0x0000000000000000000000000000000000000000000000000000000000000000"
    y = "0x0000000000000000000000000000000000000000000000000000000000000000"
    is_infinite = false

[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators]]
[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request]
sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request]
  sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request.pk_m]
    x = "0x0000000000000000000000000000000000000000000000000000000000000000"
    y = "0x0000000000000000000000000000000000000000000000000000000000000000"
    is_infinite = false

[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators]]
[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request]
sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request]
  sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request.pk_m]
    x = "0x0000000000000000000000000000000000000000000000000000000000000000"
    y = "0x0000000000000000000000000000000000000000000000000000000000000000"
    is_infinite = false

[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators]]
[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request]
sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request]
  sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request.pk_m]
    x = "0x0000000000000000000000000000000000000000000000000000000000000000"
    y = "0x0000000000000000000000000000000000000000000000000000000000000000"
    is_infinite = false

[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators]]
[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request]
sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request]
  sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request.pk_m]
    x = "0x0000000000000000000000000000000000000000000000000000000000000000"
    y = "0x0000000000000000000000000000000000000000000000000000000000000000"
    is_infinite = false

[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators]]
[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request]
sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request]
  sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request.pk_m]
    x = "0x0000000000000000000000000000000000000000000000000000000000000000"
    y = "0x0000000000000000000000000000000000000000000000000000000000000000"
    is_infinite = false

[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators]]
[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request]
sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request]
  sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request.pk_m]
    x = "0x0000000000000000000000000000000000000000000000000000000000000000"
    y = "0x0000000000000000000000000000000000000000000000000000000000000000"
    is_infinite = false

[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators]]
[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request]
sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request]
  sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request.pk_m]
    x = "0x0000000000000000000000000000000000000000000000000000000000000000"
    y = "0x0000000000000000000000000000000000000000000000000000000000000000"
    is_infinite = false

[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators]]
[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request]
sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request]
  sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request.pk_m]
    x = "0x0000000000000000000000000000000000000000000000000000000000000000"
    y = "0x0000000000000000000000000000000000000000000000000000000000000000"
    is_infinite = false

[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators]]
[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request]
sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request]
  sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request.pk_m]
    x = "0x0000000000000000000000000000000000000000000000000000000000000000"
    y = "0x0000000000000000000000000000000000000000000000000000000000000000"
    is_infinite = false

[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators]]
[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request]
sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request]
  sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request.pk_m]
    x = "0x0000000000000000000000000000000000000000000000000000000000000000"
    y = "0x0000000000000000000000000000000000000000000000000000000000000000"
    is_infinite = false

[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators]]
[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request]
sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request]
  sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request.pk_m]
    x = "0x0000000000000000000000000000000000000000000000000000000000000000"
    y = "0x0000000000000000000000000000000000000000000000000000000000000000"
    is_infinite = false

[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators]]
[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request]
sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request]
  sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request.pk_m]
    x = "0x0000000000000000000000000000000000000000000000000000000000000000"
    y = "0x0000000000000000000000000000000000000000000000000000000000000000"
    is_infinite = false

[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators]]
[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request]
sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request]
  sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request.pk_m]
    x = "0x0000000000000000000000000000000000000000000000000000000000000000"
    y = "0x0000000000000000000000000000000000000000000000000000000000000000"
    is_infinite = false

[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators]]
[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request]
sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request]
  sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request.pk_m]
    x = "0x0000000000000000000000000000000000000000000000000000000000000000"
    y = "0x0000000000000000000000000000000000000000000000000000000000000000"
    is_infinite = false

[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators]]
[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request]
sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request]
  sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request.pk_m]
    x = "0x0000000000000000000000000000000000000000000000000000000000000000"
    y = "0x0000000000000000000000000000000000000000000000000000000000000000"
    is_infinite = false

[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators]]
[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request]
sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request]
  sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request.pk_m]
    x = "0x0000000000000000000000000000000000000000000000000000000000000000"
    y = "0x0000000000000000000000000000000000000000000000000000000000000000"
    is_infinite = false

[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators]]
[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request]
sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request]
  sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request.pk_m]
    x = "0x0000000000000000000000000000000000000000000000000000000000000000"
    y = "0x0000000000000000000000000000000000000000000000000000000000000000"
    is_infinite = false

[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators]]
[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request]
sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request]
  sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request.pk_m]
    x = "0x0000000000000000000000000000000000000000000000000000000000000000"
    y = "0x0000000000000000000000000000000000000000000000000000000000000000"
    is_infinite = false

[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators]]
[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request]
sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request]
  sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request.pk_m]
    x = "0x0000000000000000000000000000000000000000000000000000000000000000"
    y = "0x0000000000000000000000000000000000000000000000000000000000000000"
    is_infinite = false

[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators]]
[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request]
sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request]
  sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request.pk_m]
    x = "0x0000000000000000000000000000000000000000000000000000000000000000"
    y = "0x0000000000000000000000000000000000000000000000000000000000000000"
    is_infinite = false

[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators]]
[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request]
sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request]
  sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request.pk_m]
    x = "0x0000000000000000000000000000000000000000000000000000000000000000"
    y = "0x0000000000000000000000000000000000000000000000000000000000000000"
    is_infinite = false

[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators]]
[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request]
sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request]
  sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request.pk_m]
    x = "0x0000000000000000000000000000000000000000000000000000000000000000"
    y = "0x0000000000000000000000000000000000000000000000000000000000000000"
    is_infinite = false

[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators]]
[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request]
sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request]
  sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request.pk_m]
    x = "0x0000000000000000000000000000000000000000000000000000000000000000"
    y = "0x0000000000000000000000000000000000000000000000000000000000000000"
    is_infinite = false

[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators]]
[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request]
sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request]
  sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request.pk_m]
    x = "0x0000000000000000000000000000000000000000000000000000000000000000"
    y = "0x0000000000000000000000000000000000000000000000000000000000000000"
    is_infinite = false

[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators]]
[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request]
sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request]
  sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request.pk_m]
    x = "0x0000000000000000000000000000000000000000000000000000000000000000"
    y = "0x0000000000000000000000000000000000000000000000000000000000000000"
    is_infinite = false

[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators]]
[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request]
sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request]
  sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request.pk_m]
    x = "0x0000000000000000000000000000000000000000000000000000000000000000"
    y = "0x0000000000000000000000000000000000000000000000000000000000000000"
    is_infinite = false

[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators]]
[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request]
sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request]
  sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request.pk_m]
    x = "0x0000000000000000000000000000000000000000000000000000000000000000"
    y = "0x0000000000000000000000000000000000000000000000000000000000000000"
    is_infinite = false

[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators]]
[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request]
sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request]
  sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request.pk_m]
    x = "0x0000000000000000000000000000000000000000000000000000000000000000"
    y = "0x0000000000000000000000000000000000000000000000000000000000000000"
    is_infinite = false

[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators]]
[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request]
sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request]
  sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request.pk_m]
    x = "0x0000000000000000000000000000000000000000000000000000000000000000"
    y = "0x0000000000000000000000000000000000000000000000000000000000000000"
    is_infinite = false

[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators]]
[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request]
sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request]
  sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request.pk_m]
    x = "0x0000000000000000000000000000000000000000000000000000000000000000"
    y = "0x0000000000000000000000000000000000000000000000000000000000000000"
    is_infinite = false

[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators]]
[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request]
sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request]
  sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request.pk_m]
    x = "0x0000000000000000000000000000000000000000000000000000000000000000"
    y = "0x0000000000000000000000000000000000000000000000000000000000000000"
    is_infinite = false

[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators]]
[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request]
sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request]
  sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request.pk_m]
    x = "0x0000000000000000000000000000000000000000000000000000000000000000"
    y = "0x0000000000000000000000000000000000000000000000000000000000000000"
    is_infinite = false

[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators]]
[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request]
sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request]
  sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request.pk_m]
    x = "0x0000000000000000000000000000000000000000000000000000000000000000"
    y = "0x0000000000000000000000000000000000000000000000000000000000000000"
    is_infinite = false

[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators]]
[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request]
sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request]
  sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request.pk_m]
    x = "0x0000000000000000000000000000000000000000000000000000000000000000"
    y = "0x0000000000000000000000000000000000000000000000000000000000000000"
    is_infinite = false

[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators]]
[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request]
sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request]
  sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request.pk_m]
    x = "0x0000000000000000000000000000000000000000000000000000000000000000"
    y = "0x0000000000000000000000000000000000000000000000000000000000000000"
    is_infinite = false

[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators]]
[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request]
sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request]
  sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request.pk_m]
    x = "0x0000000000000000000000000000000000000000000000000000000000000000"
    y = "0x0000000000000000000000000000000000000000000000000000000000000000"
    is_infinite = false

[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators]]
[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request]
sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request]
  sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request.pk_m]
    x = "0x0000000000000000000000000000000000000000000000000000000000000000"
    y = "0x0000000000000000000000000000000000000000000000000000000000000000"
    is_infinite = false

[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators]]
[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request]
sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request]
  sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request.pk_m]
    x = "0x0000000000000000000000000000000000000000000000000000000000000000"
    y = "0x0000000000000000000000000000000000000000000000000000000000000000"
    is_infinite = false

[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators]]
[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request]
sk_app_generator = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request]
  sk_app = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.request.request.pk_m]
    x = "0x0000000000000000000000000000000000000000000000000000000000000000"
    y = "0x0000000000000000000000000000000000000000000000000000000000000000"
    is_infinite = false

[previous_kernel_public_inputs.validation_requests.scoped_key_validation_requests_and_generators.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.validation_requests.split_counter]
_is_some = true
_value = "0x0000000000000000000000000000000000000000000000000000000000000003"

[[previous_kernel_public_inputs.end.note_hashes]]
[previous_kernel_public_inputs.end.note_hashes.note_hash]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.note_hashes.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.note_hashes]]
[previous_kernel_public_inputs.end.note_hashes.note_hash]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.note_hashes.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.note_hashes]]
[previous_kernel_public_inputs.end.note_hashes.note_hash]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.note_hashes.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.note_hashes]]
[previous_kernel_public_inputs.end.note_hashes.note_hash]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.note_hashes.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.note_hashes]]
[previous_kernel_public_inputs.end.note_hashes.note_hash]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.note_hashes.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.note_hashes]]
[previous_kernel_public_inputs.end.note_hashes.note_hash]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.note_hashes.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.note_hashes]]
[previous_kernel_public_inputs.end.note_hashes.note_hash]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.note_hashes.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.note_hashes]]
[previous_kernel_public_inputs.end.note_hashes.note_hash]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.note_hashes.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.note_hashes]]
[previous_kernel_public_inputs.end.note_hashes.note_hash]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.note_hashes.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.note_hashes]]
[previous_kernel_public_inputs.end.note_hashes.note_hash]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.note_hashes.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.note_hashes]]
[previous_kernel_public_inputs.end.note_hashes.note_hash]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.note_hashes.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.note_hashes]]
[previous_kernel_public_inputs.end.note_hashes.note_hash]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.note_hashes.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.note_hashes]]
[previous_kernel_public_inputs.end.note_hashes.note_hash]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.note_hashes.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.note_hashes]]
[previous_kernel_public_inputs.end.note_hashes.note_hash]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.note_hashes.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.note_hashes]]
[previous_kernel_public_inputs.end.note_hashes.note_hash]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.note_hashes.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.note_hashes]]
[previous_kernel_public_inputs.end.note_hashes.note_hash]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.note_hashes.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.note_hashes]]
[previous_kernel_public_inputs.end.note_hashes.note_hash]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.note_hashes.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.note_hashes]]
[previous_kernel_public_inputs.end.note_hashes.note_hash]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.note_hashes.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.note_hashes]]
[previous_kernel_public_inputs.end.note_hashes.note_hash]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.note_hashes.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.note_hashes]]
[previous_kernel_public_inputs.end.note_hashes.note_hash]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.note_hashes.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.note_hashes]]
[previous_kernel_public_inputs.end.note_hashes.note_hash]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.note_hashes.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.note_hashes]]
[previous_kernel_public_inputs.end.note_hashes.note_hash]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.note_hashes.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.note_hashes]]
[previous_kernel_public_inputs.end.note_hashes.note_hash]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.note_hashes.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.note_hashes]]
[previous_kernel_public_inputs.end.note_hashes.note_hash]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.note_hashes.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.note_hashes]]
[previous_kernel_public_inputs.end.note_hashes.note_hash]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.note_hashes.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.note_hashes]]
[previous_kernel_public_inputs.end.note_hashes.note_hash]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.note_hashes.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.note_hashes]]
[previous_kernel_public_inputs.end.note_hashes.note_hash]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.note_hashes.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.note_hashes]]
[previous_kernel_public_inputs.end.note_hashes.note_hash]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.note_hashes.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.note_hashes]]
[previous_kernel_public_inputs.end.note_hashes.note_hash]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.note_hashes.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.note_hashes]]
[previous_kernel_public_inputs.end.note_hashes.note_hash]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.note_hashes.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.note_hashes]]
[previous_kernel_public_inputs.end.note_hashes.note_hash]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.note_hashes.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.note_hashes]]
[previous_kernel_public_inputs.end.note_hashes.note_hash]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.note_hashes.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.note_hashes]]
[previous_kernel_public_inputs.end.note_hashes.note_hash]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.note_hashes.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.note_hashes]]
[previous_kernel_public_inputs.end.note_hashes.note_hash]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.note_hashes.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.note_hashes]]
[previous_kernel_public_inputs.end.note_hashes.note_hash]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.note_hashes.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.note_hashes]]
[previous_kernel_public_inputs.end.note_hashes.note_hash]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.note_hashes.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.note_hashes]]
[previous_kernel_public_inputs.end.note_hashes.note_hash]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.note_hashes.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.note_hashes]]
[previous_kernel_public_inputs.end.note_hashes.note_hash]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.note_hashes.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.note_hashes]]
[previous_kernel_public_inputs.end.note_hashes.note_hash]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.note_hashes.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.note_hashes]]
[previous_kernel_public_inputs.end.note_hashes.note_hash]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.note_hashes.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.note_hashes]]
[previous_kernel_public_inputs.end.note_hashes.note_hash]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.note_hashes.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.note_hashes]]
[previous_kernel_public_inputs.end.note_hashes.note_hash]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.note_hashes.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.note_hashes]]
[previous_kernel_public_inputs.end.note_hashes.note_hash]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.note_hashes.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.note_hashes]]
[previous_kernel_public_inputs.end.note_hashes.note_hash]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.note_hashes.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.note_hashes]]
[previous_kernel_public_inputs.end.note_hashes.note_hash]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.note_hashes.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.note_hashes]]
[previous_kernel_public_inputs.end.note_hashes.note_hash]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.note_hashes.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.note_hashes]]
[previous_kernel_public_inputs.end.note_hashes.note_hash]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.note_hashes.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.note_hashes]]
[previous_kernel_public_inputs.end.note_hashes.note_hash]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.note_hashes.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.note_hashes]]
[previous_kernel_public_inputs.end.note_hashes.note_hash]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.note_hashes.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.note_hashes]]
[previous_kernel_public_inputs.end.note_hashes.note_hash]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.note_hashes.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.note_hashes]]
[previous_kernel_public_inputs.end.note_hashes.note_hash]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.note_hashes.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.note_hashes]]
[previous_kernel_public_inputs.end.note_hashes.note_hash]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.note_hashes.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.note_hashes]]
[previous_kernel_public_inputs.end.note_hashes.note_hash]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.note_hashes.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.note_hashes]]
[previous_kernel_public_inputs.end.note_hashes.note_hash]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.note_hashes.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.note_hashes]]
[previous_kernel_public_inputs.end.note_hashes.note_hash]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.note_hashes.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.note_hashes]]
[previous_kernel_public_inputs.end.note_hashes.note_hash]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.note_hashes.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.note_hashes]]
[previous_kernel_public_inputs.end.note_hashes.note_hash]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.note_hashes.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.note_hashes]]
[previous_kernel_public_inputs.end.note_hashes.note_hash]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.note_hashes.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.note_hashes]]
[previous_kernel_public_inputs.end.note_hashes.note_hash]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.note_hashes.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.note_hashes]]
[previous_kernel_public_inputs.end.note_hashes.note_hash]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.note_hashes.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.note_hashes]]
[previous_kernel_public_inputs.end.note_hashes.note_hash]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.note_hashes.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.note_hashes]]
[previous_kernel_public_inputs.end.note_hashes.note_hash]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.note_hashes.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.note_hashes]]
[previous_kernel_public_inputs.end.note_hashes.note_hash]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.note_hashes.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.note_hashes]]
[previous_kernel_public_inputs.end.note_hashes.note_hash]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.note_hashes.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.nullifiers]]
[previous_kernel_public_inputs.end.nullifiers.nullifier]
value = "0x145c69e0317226be0054019c8f00cdd1f74fe1a781654f4954f7211c559b896b"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.nullifiers.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.nullifiers]]
[previous_kernel_public_inputs.end.nullifiers.nullifier]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.nullifiers.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.nullifiers]]
[previous_kernel_public_inputs.end.nullifiers.nullifier]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.nullifiers.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.nullifiers]]
[previous_kernel_public_inputs.end.nullifiers.nullifier]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.nullifiers.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.nullifiers]]
[previous_kernel_public_inputs.end.nullifiers.nullifier]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.nullifiers.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.nullifiers]]
[previous_kernel_public_inputs.end.nullifiers.nullifier]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.nullifiers.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.nullifiers]]
[previous_kernel_public_inputs.end.nullifiers.nullifier]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.nullifiers.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.nullifiers]]
[previous_kernel_public_inputs.end.nullifiers.nullifier]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.nullifiers.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.nullifiers]]
[previous_kernel_public_inputs.end.nullifiers.nullifier]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.nullifiers.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.nullifiers]]
[previous_kernel_public_inputs.end.nullifiers.nullifier]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.nullifiers.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.nullifiers]]
[previous_kernel_public_inputs.end.nullifiers.nullifier]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.nullifiers.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.nullifiers]]
[previous_kernel_public_inputs.end.nullifiers.nullifier]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.nullifiers.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.nullifiers]]
[previous_kernel_public_inputs.end.nullifiers.nullifier]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.nullifiers.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.nullifiers]]
[previous_kernel_public_inputs.end.nullifiers.nullifier]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.nullifiers.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.nullifiers]]
[previous_kernel_public_inputs.end.nullifiers.nullifier]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.nullifiers.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.nullifiers]]
[previous_kernel_public_inputs.end.nullifiers.nullifier]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.nullifiers.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.nullifiers]]
[previous_kernel_public_inputs.end.nullifiers.nullifier]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.nullifiers.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.nullifiers]]
[previous_kernel_public_inputs.end.nullifiers.nullifier]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.nullifiers.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.nullifiers]]
[previous_kernel_public_inputs.end.nullifiers.nullifier]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.nullifiers.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.nullifiers]]
[previous_kernel_public_inputs.end.nullifiers.nullifier]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.nullifiers.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.nullifiers]]
[previous_kernel_public_inputs.end.nullifiers.nullifier]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.nullifiers.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.nullifiers]]
[previous_kernel_public_inputs.end.nullifiers.nullifier]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.nullifiers.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.nullifiers]]
[previous_kernel_public_inputs.end.nullifiers.nullifier]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.nullifiers.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.nullifiers]]
[previous_kernel_public_inputs.end.nullifiers.nullifier]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.nullifiers.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.nullifiers]]
[previous_kernel_public_inputs.end.nullifiers.nullifier]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.nullifiers.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.nullifiers]]
[previous_kernel_public_inputs.end.nullifiers.nullifier]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.nullifiers.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.nullifiers]]
[previous_kernel_public_inputs.end.nullifiers.nullifier]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.nullifiers.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.nullifiers]]
[previous_kernel_public_inputs.end.nullifiers.nullifier]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.nullifiers.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.nullifiers]]
[previous_kernel_public_inputs.end.nullifiers.nullifier]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.nullifiers.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.nullifiers]]
[previous_kernel_public_inputs.end.nullifiers.nullifier]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.nullifiers.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.nullifiers]]
[previous_kernel_public_inputs.end.nullifiers.nullifier]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.nullifiers.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.nullifiers]]
[previous_kernel_public_inputs.end.nullifiers.nullifier]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.nullifiers.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.nullifiers]]
[previous_kernel_public_inputs.end.nullifiers.nullifier]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.nullifiers.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.nullifiers]]
[previous_kernel_public_inputs.end.nullifiers.nullifier]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.nullifiers.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.nullifiers]]
[previous_kernel_public_inputs.end.nullifiers.nullifier]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.nullifiers.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.nullifiers]]
[previous_kernel_public_inputs.end.nullifiers.nullifier]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.nullifiers.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.nullifiers]]
[previous_kernel_public_inputs.end.nullifiers.nullifier]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.nullifiers.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.nullifiers]]
[previous_kernel_public_inputs.end.nullifiers.nullifier]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.nullifiers.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.nullifiers]]
[previous_kernel_public_inputs.end.nullifiers.nullifier]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.nullifiers.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.nullifiers]]
[previous_kernel_public_inputs.end.nullifiers.nullifier]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.nullifiers.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.nullifiers]]
[previous_kernel_public_inputs.end.nullifiers.nullifier]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.nullifiers.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.nullifiers]]
[previous_kernel_public_inputs.end.nullifiers.nullifier]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.nullifiers.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.nullifiers]]
[previous_kernel_public_inputs.end.nullifiers.nullifier]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.nullifiers.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.nullifiers]]
[previous_kernel_public_inputs.end.nullifiers.nullifier]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.nullifiers.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.nullifiers]]
[previous_kernel_public_inputs.end.nullifiers.nullifier]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.nullifiers.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.nullifiers]]
[previous_kernel_public_inputs.end.nullifiers.nullifier]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.nullifiers.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.nullifiers]]
[previous_kernel_public_inputs.end.nullifiers.nullifier]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.nullifiers.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.nullifiers]]
[previous_kernel_public_inputs.end.nullifiers.nullifier]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.nullifiers.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.nullifiers]]
[previous_kernel_public_inputs.end.nullifiers.nullifier]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.nullifiers.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.nullifiers]]
[previous_kernel_public_inputs.end.nullifiers.nullifier]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.nullifiers.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.nullifiers]]
[previous_kernel_public_inputs.end.nullifiers.nullifier]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.nullifiers.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.nullifiers]]
[previous_kernel_public_inputs.end.nullifiers.nullifier]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.nullifiers.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.nullifiers]]
[previous_kernel_public_inputs.end.nullifiers.nullifier]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.nullifiers.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.nullifiers]]
[previous_kernel_public_inputs.end.nullifiers.nullifier]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.nullifiers.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.nullifiers]]
[previous_kernel_public_inputs.end.nullifiers.nullifier]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.nullifiers.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.nullifiers]]
[previous_kernel_public_inputs.end.nullifiers.nullifier]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.nullifiers.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.nullifiers]]
[previous_kernel_public_inputs.end.nullifiers.nullifier]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.nullifiers.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.nullifiers]]
[previous_kernel_public_inputs.end.nullifiers.nullifier]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.nullifiers.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.nullifiers]]
[previous_kernel_public_inputs.end.nullifiers.nullifier]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.nullifiers.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.nullifiers]]
[previous_kernel_public_inputs.end.nullifiers.nullifier]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.nullifiers.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.nullifiers]]
[previous_kernel_public_inputs.end.nullifiers.nullifier]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.nullifiers.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.nullifiers]]
[previous_kernel_public_inputs.end.nullifiers.nullifier]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.nullifiers.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.nullifiers]]
[previous_kernel_public_inputs.end.nullifiers.nullifier]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.nullifiers.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.nullifiers]]
[previous_kernel_public_inputs.end.nullifiers.nullifier]
value = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
note_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.nullifiers.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.l2_to_l1_msgs]]
[previous_kernel_public_inputs.end.l2_to_l1_msgs.inner]
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.l2_to_l1_msgs.inner.inner]
  content = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.l2_to_l1_msgs.inner.inner.recipient]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.l2_to_l1_msgs.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.l2_to_l1_msgs]]
[previous_kernel_public_inputs.end.l2_to_l1_msgs.inner]
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.l2_to_l1_msgs.inner.inner]
  content = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.l2_to_l1_msgs.inner.inner.recipient]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.l2_to_l1_msgs.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.l2_to_l1_msgs]]
[previous_kernel_public_inputs.end.l2_to_l1_msgs.inner]
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.l2_to_l1_msgs.inner.inner]
  content = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.l2_to_l1_msgs.inner.inner.recipient]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.l2_to_l1_msgs.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.l2_to_l1_msgs]]
[previous_kernel_public_inputs.end.l2_to_l1_msgs.inner]
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.l2_to_l1_msgs.inner.inner]
  content = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.l2_to_l1_msgs.inner.inner.recipient]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.l2_to_l1_msgs.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.l2_to_l1_msgs]]
[previous_kernel_public_inputs.end.l2_to_l1_msgs.inner]
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.l2_to_l1_msgs.inner.inner]
  content = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.l2_to_l1_msgs.inner.inner.recipient]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.l2_to_l1_msgs.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.l2_to_l1_msgs]]
[previous_kernel_public_inputs.end.l2_to_l1_msgs.inner]
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.l2_to_l1_msgs.inner.inner]
  content = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.l2_to_l1_msgs.inner.inner.recipient]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.l2_to_l1_msgs.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.l2_to_l1_msgs]]
[previous_kernel_public_inputs.end.l2_to_l1_msgs.inner]
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.l2_to_l1_msgs.inner.inner]
  content = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.l2_to_l1_msgs.inner.inner.recipient]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.l2_to_l1_msgs.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.l2_to_l1_msgs]]
[previous_kernel_public_inputs.end.l2_to_l1_msgs.inner]
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.l2_to_l1_msgs.inner.inner]
  content = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.l2_to_l1_msgs.inner.inner.recipient]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.l2_to_l1_msgs.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.private_logs]]
[previous_kernel_public_inputs.end.private_logs.inner]
note_hash_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.private_logs.inner.log]
  fields = [
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000"
]
  length = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.private_logs.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.private_logs]]
[previous_kernel_public_inputs.end.private_logs.inner]
note_hash_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.private_logs.inner.log]
  fields = [
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000"
]
  length = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.private_logs.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.private_logs]]
[previous_kernel_public_inputs.end.private_logs.inner]
note_hash_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.private_logs.inner.log]
  fields = [
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000"
]
  length = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.private_logs.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.private_logs]]
[previous_kernel_public_inputs.end.private_logs.inner]
note_hash_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.private_logs.inner.log]
  fields = [
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000"
]
  length = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.private_logs.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.private_logs]]
[previous_kernel_public_inputs.end.private_logs.inner]
note_hash_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.private_logs.inner.log]
  fields = [
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000"
]
  length = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.private_logs.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.private_logs]]
[previous_kernel_public_inputs.end.private_logs.inner]
note_hash_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.private_logs.inner.log]
  fields = [
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000"
]
  length = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.private_logs.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.private_logs]]
[previous_kernel_public_inputs.end.private_logs.inner]
note_hash_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.private_logs.inner.log]
  fields = [
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000"
]
  length = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.private_logs.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.private_logs]]
[previous_kernel_public_inputs.end.private_logs.inner]
note_hash_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.private_logs.inner.log]
  fields = [
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000"
]
  length = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.private_logs.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.private_logs]]
[previous_kernel_public_inputs.end.private_logs.inner]
note_hash_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.private_logs.inner.log]
  fields = [
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000"
]
  length = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.private_logs.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.private_logs]]
[previous_kernel_public_inputs.end.private_logs.inner]
note_hash_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.private_logs.inner.log]
  fields = [
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000"
]
  length = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.private_logs.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.private_logs]]
[previous_kernel_public_inputs.end.private_logs.inner]
note_hash_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.private_logs.inner.log]
  fields = [
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000"
]
  length = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.private_logs.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.private_logs]]
[previous_kernel_public_inputs.end.private_logs.inner]
note_hash_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.private_logs.inner.log]
  fields = [
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000"
]
  length = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.private_logs.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.private_logs]]
[previous_kernel_public_inputs.end.private_logs.inner]
note_hash_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.private_logs.inner.log]
  fields = [
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000"
]
  length = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.private_logs.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.private_logs]]
[previous_kernel_public_inputs.end.private_logs.inner]
note_hash_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.private_logs.inner.log]
  fields = [
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000"
]
  length = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.private_logs.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.private_logs]]
[previous_kernel_public_inputs.end.private_logs.inner]
note_hash_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.private_logs.inner.log]
  fields = [
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000"
]
  length = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.private_logs.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.private_logs]]
[previous_kernel_public_inputs.end.private_logs.inner]
note_hash_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.private_logs.inner.log]
  fields = [
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000"
]
  length = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.private_logs.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.private_logs]]
[previous_kernel_public_inputs.end.private_logs.inner]
note_hash_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.private_logs.inner.log]
  fields = [
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000"
]
  length = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.private_logs.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.private_logs]]
[previous_kernel_public_inputs.end.private_logs.inner]
note_hash_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.private_logs.inner.log]
  fields = [
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000"
]
  length = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.private_logs.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.private_logs]]
[previous_kernel_public_inputs.end.private_logs.inner]
note_hash_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.private_logs.inner.log]
  fields = [
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000"
]
  length = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.private_logs.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.private_logs]]
[previous_kernel_public_inputs.end.private_logs.inner]
note_hash_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.private_logs.inner.log]
  fields = [
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000"
]
  length = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.private_logs.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.private_logs]]
[previous_kernel_public_inputs.end.private_logs.inner]
note_hash_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.private_logs.inner.log]
  fields = [
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000"
]
  length = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.private_logs.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.private_logs]]
[previous_kernel_public_inputs.end.private_logs.inner]
note_hash_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.private_logs.inner.log]
  fields = [
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000"
]
  length = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.private_logs.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.private_logs]]
[previous_kernel_public_inputs.end.private_logs.inner]
note_hash_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.private_logs.inner.log]
  fields = [
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000"
]
  length = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.private_logs.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.private_logs]]
[previous_kernel_public_inputs.end.private_logs.inner]
note_hash_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.private_logs.inner.log]
  fields = [
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000"
]
  length = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.private_logs.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.private_logs]]
[previous_kernel_public_inputs.end.private_logs.inner]
note_hash_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.private_logs.inner.log]
  fields = [
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000"
]
  length = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.private_logs.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.private_logs]]
[previous_kernel_public_inputs.end.private_logs.inner]
note_hash_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.private_logs.inner.log]
  fields = [
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000"
]
  length = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.private_logs.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.private_logs]]
[previous_kernel_public_inputs.end.private_logs.inner]
note_hash_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.private_logs.inner.log]
  fields = [
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000"
]
  length = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.private_logs.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.private_logs]]
[previous_kernel_public_inputs.end.private_logs.inner]
note_hash_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.private_logs.inner.log]
  fields = [
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000"
]
  length = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.private_logs.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.private_logs]]
[previous_kernel_public_inputs.end.private_logs.inner]
note_hash_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.private_logs.inner.log]
  fields = [
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000"
]
  length = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.private_logs.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.private_logs]]
[previous_kernel_public_inputs.end.private_logs.inner]
note_hash_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.private_logs.inner.log]
  fields = [
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000"
]
  length = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.private_logs.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.private_logs]]
[previous_kernel_public_inputs.end.private_logs.inner]
note_hash_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.private_logs.inner.log]
  fields = [
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000"
]
  length = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.private_logs.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.private_logs]]
[previous_kernel_public_inputs.end.private_logs.inner]
note_hash_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.private_logs.inner.log]
  fields = [
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000",
  "0x0000000000000000000000000000000000000000000000000000000000000000"
]
  length = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.private_logs.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.contract_class_logs_hashes]]
[previous_kernel_public_inputs.end.contract_class_logs_hashes.inner]
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.contract_class_logs_hashes.inner.inner]
  value = "0x0000000000000000000000000000000000000000000000000000000000000000"
  length = "0x0000000000000000000000000000000000000000000000000000000000000000"

[previous_kernel_public_inputs.end.contract_class_logs_hashes.contract_address]
inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.public_call_requests]]
counter = "0x0000000000000000000000000000000000000000000000000000000000000003"

  [previous_kernel_public_inputs.end.public_call_requests.inner]
  is_static_call = true
  calldata_hash = "0x0753b92cfce6df3af08d88c8bad668e96021a774ac6adb79775e0bd5b828146d"

    [previous_kernel_public_inputs.end.public_call_requests.inner.msg_sender]
    inner = "0x2bd6622f715bd307d4918e462bd9aa829794c19bfe8ebbe5390237361e65e437"

    [previous_kernel_public_inputs.end.public_call_requests.inner.contract_address]
    inner = "0x2aa83d8a54e3931f79d1923248fa2aafa441fbd0e8df1989f1fb70a910a49471"

[[previous_kernel_public_inputs.end.public_call_requests]]
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.public_call_requests.inner]
  is_static_call = false
  calldata_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.public_call_requests.inner.msg_sender]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.public_call_requests.inner.contract_address]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.public_call_requests]]
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.public_call_requests.inner]
  is_static_call = false
  calldata_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.public_call_requests.inner.msg_sender]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.public_call_requests.inner.contract_address]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.public_call_requests]]
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.public_call_requests.inner]
  is_static_call = false
  calldata_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.public_call_requests.inner.msg_sender]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.public_call_requests.inner.contract_address]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.public_call_requests]]
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.public_call_requests.inner]
  is_static_call = false
  calldata_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.public_call_requests.inner.msg_sender]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.public_call_requests.inner.contract_address]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.public_call_requests]]
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.public_call_requests.inner]
  is_static_call = false
  calldata_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.public_call_requests.inner.msg_sender]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.public_call_requests.inner.contract_address]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.public_call_requests]]
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.public_call_requests.inner]
  is_static_call = false
  calldata_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.public_call_requests.inner.msg_sender]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.public_call_requests.inner.contract_address]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.public_call_requests]]
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.public_call_requests.inner]
  is_static_call = false
  calldata_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.public_call_requests.inner.msg_sender]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.public_call_requests.inner.contract_address]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.public_call_requests]]
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.public_call_requests.inner]
  is_static_call = false
  calldata_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.public_call_requests.inner.msg_sender]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.public_call_requests.inner.contract_address]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.public_call_requests]]
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.public_call_requests.inner]
  is_static_call = false
  calldata_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.public_call_requests.inner.msg_sender]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.public_call_requests.inner.contract_address]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.public_call_requests]]
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.public_call_requests.inner]
  is_static_call = false
  calldata_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.public_call_requests.inner.msg_sender]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.public_call_requests.inner.contract_address]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.public_call_requests]]
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.public_call_requests.inner]
  is_static_call = false
  calldata_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.public_call_requests.inner.msg_sender]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.public_call_requests.inner.contract_address]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.public_call_requests]]
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.public_call_requests.inner]
  is_static_call = false
  calldata_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.public_call_requests.inner.msg_sender]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.public_call_requests.inner.contract_address]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.public_call_requests]]
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.public_call_requests.inner]
  is_static_call = false
  calldata_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.public_call_requests.inner.msg_sender]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.public_call_requests.inner.contract_address]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.public_call_requests]]
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.public_call_requests.inner]
  is_static_call = false
  calldata_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.public_call_requests.inner.msg_sender]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.public_call_requests.inner.contract_address]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.public_call_requests]]
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.public_call_requests.inner]
  is_static_call = false
  calldata_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.public_call_requests.inner.msg_sender]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.public_call_requests.inner.contract_address]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.public_call_requests]]
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.public_call_requests.inner]
  is_static_call = false
  calldata_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.public_call_requests.inner.msg_sender]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.public_call_requests.inner.contract_address]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.public_call_requests]]
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.public_call_requests.inner]
  is_static_call = false
  calldata_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.public_call_requests.inner.msg_sender]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.public_call_requests.inner.contract_address]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.public_call_requests]]
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.public_call_requests.inner]
  is_static_call = false
  calldata_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.public_call_requests.inner.msg_sender]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.public_call_requests.inner.contract_address]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.public_call_requests]]
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.public_call_requests.inner]
  is_static_call = false
  calldata_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.public_call_requests.inner.msg_sender]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.public_call_requests.inner.contract_address]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.public_call_requests]]
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.public_call_requests.inner]
  is_static_call = false
  calldata_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.public_call_requests.inner.msg_sender]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.public_call_requests.inner.contract_address]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.public_call_requests]]
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.public_call_requests.inner]
  is_static_call = false
  calldata_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.public_call_requests.inner.msg_sender]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.public_call_requests.inner.contract_address]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.public_call_requests]]
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.public_call_requests.inner]
  is_static_call = false
  calldata_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.public_call_requests.inner.msg_sender]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.public_call_requests.inner.contract_address]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.public_call_requests]]
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.public_call_requests.inner]
  is_static_call = false
  calldata_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.public_call_requests.inner.msg_sender]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.public_call_requests.inner.contract_address]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.public_call_requests]]
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.public_call_requests.inner]
  is_static_call = false
  calldata_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.public_call_requests.inner.msg_sender]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.public_call_requests.inner.contract_address]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.public_call_requests]]
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.public_call_requests.inner]
  is_static_call = false
  calldata_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.public_call_requests.inner.msg_sender]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.public_call_requests.inner.contract_address]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.public_call_requests]]
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.public_call_requests.inner]
  is_static_call = false
  calldata_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.public_call_requests.inner.msg_sender]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.public_call_requests.inner.contract_address]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.public_call_requests]]
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.public_call_requests.inner]
  is_static_call = false
  calldata_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.public_call_requests.inner.msg_sender]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.public_call_requests.inner.contract_address]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.public_call_requests]]
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.public_call_requests.inner]
  is_static_call = false
  calldata_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.public_call_requests.inner.msg_sender]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.public_call_requests.inner.contract_address]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.public_call_requests]]
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.public_call_requests.inner]
  is_static_call = false
  calldata_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.public_call_requests.inner.msg_sender]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.public_call_requests.inner.contract_address]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.public_call_requests]]
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.public_call_requests.inner]
  is_static_call = false
  calldata_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.public_call_requests.inner.msg_sender]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.public_call_requests.inner.contract_address]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.public_call_requests]]
counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.public_call_requests.inner]
  is_static_call = false
  calldata_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.public_call_requests.inner.msg_sender]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.public_call_requests.inner.contract_address]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.private_call_stack]]
args_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"
returns_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"
start_side_effect_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
end_side_effect_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.private_call_stack.call_context]
  is_static_call = false

    [previous_kernel_public_inputs.end.private_call_stack.call_context.msg_sender]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.private_call_stack.call_context.contract_address]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.private_call_stack.call_context.function_selector]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.private_call_stack]]
args_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"
returns_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"
start_side_effect_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
end_side_effect_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.private_call_stack.call_context]
  is_static_call = false

    [previous_kernel_public_inputs.end.private_call_stack.call_context.msg_sender]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.private_call_stack.call_context.contract_address]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.private_call_stack.call_context.function_selector]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.private_call_stack]]
args_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"
returns_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"
start_side_effect_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
end_side_effect_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.private_call_stack.call_context]
  is_static_call = false

    [previous_kernel_public_inputs.end.private_call_stack.call_context.msg_sender]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.private_call_stack.call_context.contract_address]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.private_call_stack.call_context.function_selector]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.private_call_stack]]
args_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"
returns_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"
start_side_effect_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
end_side_effect_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.private_call_stack.call_context]
  is_static_call = false

    [previous_kernel_public_inputs.end.private_call_stack.call_context.msg_sender]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.private_call_stack.call_context.contract_address]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.private_call_stack.call_context.function_selector]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.private_call_stack]]
args_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"
returns_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"
start_side_effect_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
end_side_effect_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.private_call_stack.call_context]
  is_static_call = false

    [previous_kernel_public_inputs.end.private_call_stack.call_context.msg_sender]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.private_call_stack.call_context.contract_address]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.private_call_stack.call_context.function_selector]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.private_call_stack]]
args_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"
returns_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"
start_side_effect_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
end_side_effect_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.private_call_stack.call_context]
  is_static_call = false

    [previous_kernel_public_inputs.end.private_call_stack.call_context.msg_sender]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.private_call_stack.call_context.contract_address]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.private_call_stack.call_context.function_selector]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.private_call_stack]]
args_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"
returns_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"
start_side_effect_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
end_side_effect_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.private_call_stack.call_context]
  is_static_call = false

    [previous_kernel_public_inputs.end.private_call_stack.call_context.msg_sender]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.private_call_stack.call_context.contract_address]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.private_call_stack.call_context.function_selector]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

[[previous_kernel_public_inputs.end.private_call_stack]]
args_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"
returns_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"
start_side_effect_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"
end_side_effect_counter = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.end.private_call_stack.call_context]
  is_static_call = false

    [previous_kernel_public_inputs.end.private_call_stack.call_context.msg_sender]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.private_call_stack.call_context.contract_address]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.end.private_call_stack.call_context.function_selector]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.public_teardown_call_request]
  is_static_call = false
  calldata_hash = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.public_teardown_call_request.msg_sender]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

    [previous_kernel_public_inputs.public_teardown_call_request.contract_address]
    inner = "0x0000000000000000000000000000000000000000000000000000000000000000"

  [previous_kernel_public_inputs.fee_payer]
  inner = "0x2bd6622f715bd307d4918e462bd9aa829794c19bfe8ebbe5390237361e65e437"

[padded_side_effect_amounts]
non_revertible_note_hashes = "0x0000000000000000000000000000000000000000000000000000000000000000"
revertible_note_hashes = "0x0000000000000000000000000000000000000000000000000000000000000000"
non_revertible_nullifiers = "0x0000000000000000000000000000000000000000000000000000000000000000"
revertible_nullifiers = "0x0000000000000000000000000000000000000000000000000000000000000000"
non_revertible_private_logs = "0x0000000000000000000000000000000000000000000000000000000000000000"
revertible_private_logs = "0x0000000000000000000000000000000000000000000000000000000000000000"
