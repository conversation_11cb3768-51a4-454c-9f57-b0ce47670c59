// Gas and fees
export const TX_ERROR_INSUFFICIENT_FEE_PER_GAS = 'Insufficient fee per gas';
export const TX_ERROR_INSUFFICIENT_FEE_PAYER_BALANCE = 'Insufficient fee payer balance';
export const TX_ERROR_INSUFFICIENT_GAS_LIMIT = 'Gas limit is below the minimum fixed cost';

// Phases
export const TX_ERROR_SETUP_FUNCTION_NOT_ALLOWED = 'Setup function not on allow list';

// Nullifiers
export const TX_ERROR_DUPLICATE_NULLIFIER_IN_TX = 'Duplicate nullifier in tx';
export const TX_ERROR_EXISTING_NULLIFIER = 'Existing nullifier';

// Metadata
export const TX_ERROR_INVALID_MAX_BLOCK_NUMBER = 'Invalid max block number';
export const TX_ERROR_INCORRECT_L1_CHAIN_ID = 'Incorrect L1 chain id';
export const TX_ERROR_INCORRECT_ROLLUP_VERSION = 'Incorrect rollup version';
export const TX_ERROR_INCORRECT_VK_TREE_ROOT = 'Incorrect protocol circuits tree root';
export const TX_ERROR_INCORRECT_PROTOCOL_CONTRACT_TREE_ROOT = 'Incorrect protocol contracts tree root';

// Proof
export const TX_ERROR_INVALID_PROOF = 'Invalid proof';

//Data
export const TX_ERROR_INCORRECT_CALLDATA = 'Incorrect calldata for public call';
export const TX_ERROR_CALLDATA_COUNT_MISMATCH = 'Wrong number of calldata for public calls';
export const TX_ERROR_CALLDATA_COUNT_TOO_LARGE = 'Total calldata too large for enqueued public calls';
export const TX_ERROR_CONTRACT_CLASS_LOG_COUNT = 'Mismatched number of contract class logs';
export const TX_ERROR_CONTRACT_CLASS_LOG_LENGTH = 'Incorrect contract class logs length';
export const TX_ERROR_CONTRACT_CLASS_LOGS = 'Mismatched contract class logs';
export const TX_ERROR_CONTRACT_CLASS_LOG_SORTING = 'Incorrectly sorted contract class logs';

// Block header
export const TX_ERROR_BLOCK_HEADER = 'Block header not found';

// General
export const TX_ERROR_DURING_VALIDATION = 'Unexpected error during validation';
